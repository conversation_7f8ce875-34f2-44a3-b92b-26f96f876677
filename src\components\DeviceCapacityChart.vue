<template>
  <div class="device-capacity">
    <!-- 存储容量 -->
    <div class="capacity-row">
      <div class="capacity-icon">
        <img src="@assets/images/dashborad/桥段_正常.png" alt="" />
      </div>
      <div class="capacity-content">
        <div class="capacity-title">存储容量：</div>
        <div class="capacity-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: storageUsagePercent + '%' }"></div>
          </div>
          <div class="capacity-data">{{ Math.round(storageUsed) }}/{{ Math.round(storageTotal) }}GB</div>
        </div>
      </div>
    </div>

    <!-- GPU使用 -->
    <div class="capacity-row">
      <div class="capacity-icon">
        <img src="@assets/images/dashborad/桥段_正常 (1).png" alt="" />
      </div>
      <div class="capacity-content">
        <div class="capacity-title">CPU使用：</div>
        <div class="capacity-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: cpuUsagePercent + '%' }"></div>
          </div>
          <div class="capacity-data">{{ cpuUsed.toFixed(2) }}/{{ cpuTotal.toFixed(2) }}MHz</div>
        </div>
      </div>
    </div>

    <!-- 虚机总数 -->
    <div class="capacity-row">
      <div class="capacity-icon">
        <img src="@assets/images/dashborad/桥段_正常 (2).png" alt="" />
      </div>
      <div class="capacity-content">
        <div class="capacity-title">虚机总数：</div>
        <div class="capacity-status-group">
          <span class="status-item">
            <span class="status-number">{{ vmTotal }}</span>台
          </span>
          <span class="status-item">
            正常：<span class="status-number normal">{{ vmNormal }}</span>台
          </span>
          <span class="status-item">
            异常：<span class="status-number abnormal">{{ vmAbnormal }}</span>台
          </span>
        </div>
      </div>
    </div>

    <!-- 交换机总数 -->
    <div class="capacity-row">
      <div class="capacity-icon">
        <img src="@assets/images/dashborad/桥段_正常 (3).png" alt="" />
      </div>
      <div class="capacity-content">
        <div class="capacity-title">交换机总数：</div>
        <div class="capacity-status-group">
          <span class="status-item">
            <span class="status-number">{{ switchTotal }}</span>台
          </span>
          <span class="status-item">
            在线：<span class="status-number normal">{{ switchOnline }}</span>台
          </span>
          <span class="status-item">
            异常：<span class="status-number abnormal">{{ switchAbnormal }}</span>台
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 定义props
const props = defineProps({
  storageUsed: {
    type: Number,
    default: 0
  },
  storageTotal: {
    type: Number,
    default: 0
  },
  cpuUsed: {
    type: Number,
    default: 0
  },
  cpuTotal: {
    type: Number,
    default: 0
  }
})

// 计算属性
const storageUsagePercent = computed(() => {
  if (props.storageTotal === 0) return 0
  return (props.storageUsed / props.storageTotal) * 100
})

const cpuUsagePercent = computed(() => {
  if (props.cpuTotal === 0) return 0
  return (props.cpuUsed / props.cpuTotal) * 100
})

// GPU数据
const gpuUsed = ref(120)
const gpuTotal = ref(136)
const gpuUsagePercent = computed(() => {
  return (gpuUsed.value / gpuTotal.value) * 100
})

// 虚机数据
const vmTotal = ref(128)
const vmNormal = ref(128)
const vmAbnormal = ref(0)

// 交换机数据
const switchTotal = ref(18)
const switchOnline = ref(18)
const switchAbnormal = ref(0)
</script>

<style scoped lang="scss">
.device-capacity {
  width: 100%;
  padding: 10px 2px;
  box-sizing: border-box;
}

.capacity-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  height: 60px;
  background: url('@assets/images/dashborad/img_bg5.png') no-repeat center center / 100% 100%;
  padding: 0 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.capacity-icon {
  width: 40px;
  height: 40px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.capacity-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.capacity-title {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  white-space: nowrap;
  margin-right: 20px;
}

.capacity-progress {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 250px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin-right: 15px;
  overflow: hidden;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 4px;
    border: 1px solid rgba(39, 171, 255, 0.3);
  }
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00D4FF 0%, #27ABFF 50%, #0099CC 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(39, 171, 255, 0.5);
}

.capacity-data {
  font-family: 'DIN Alternate', Arial, sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
  white-space: nowrap;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.capacity-status-group {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-item {
  font-size: 14px;
  color: #FFFFFF;
  white-space: nowrap;

  .status-number {
    font-family: 'DIN Alternate', Arial, sans-serif;
    font-size: 18px;
    font-weight: bold;
    margin: 0 4px;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);

    &.normal {
      color: #00FF88;
      text-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
    }

    &.abnormal {
      color: #FF4444;
      text-shadow: 0 0 8px rgba(255, 68, 68, 0.5);
    }
  }
}
</style> 