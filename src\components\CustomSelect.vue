<template>
  <div class="custom-select">
    <select 
      :value="modelValue" 
      @change="handleChange"
      class="select-input"
    >
      <option 
        v-for="option in options" 
        :key="option.value" 
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    <div class="select-arrow">
      <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
        <path d="M1 1L6 6L11 1" stroke="#C0DAEA" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const handleChange = (event) => {
  const value = event.target.value
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped lang="scss">
.custom-select {
  position: relative;
  display: flex;
  width: 132px;
  height: 24px;
  
  .select-input {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(9,25,62,0.56) 0%, rgba(9,25,62,0.38) 100%);
    border-radius: 2px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(58, 149, 255, 0.26), rgba(58, 149, 255, 0.3)) 1 1;
    color: #C0DAEA;
    font-size: 13px;
    font-family: pingfang;
    padding: 0 24px 0 8px;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    box-sizing: border-box;
    
    &:focus {
      border-image: linear-gradient(180deg, rgba(58, 149, 255, 0.5), rgba(58, 149, 255, 0.6)) 1 1;
    }
    
    // 选项样式
    option {
      background: rgba(6, 25, 55, 0.95);
      color: #C0DAEA;
      font-family: pingfang;
      font-size: 12px;
      padding: 4px 8px;
    }
  }
  
  .select-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 8px;
    z-index: 1;
    
    svg {
      width: 100%;
      height: 100%;
    }
  }
}
</style> 