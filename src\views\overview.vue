<template>
  <div class="left-content">
    <div class="container">
      <div class="container-item">
        <div class="content-header">机房信息</div>
        <div class="content-body jifang-info">
          <div class="jifang-info-item" @click="openModal('cabinet')">
            <img src="@assets/images/jifang-info-item1.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">20</span>
              <span class="jifang-info-item-value-unit">台</span>
            </div>
            <div class="jifang-info-item-title">
              <span>机柜总数</span>
            </div>
          </div>
          <div class="jifang-info-item" @click="openModal('device')">
            <img src="@assets/images/jifang-info-item2.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">136</span>
              <span class="jifang-info-item-value-unit">台</span>
            </div>
            <div class="jifang-info-item-title">
              <span>设备总数</span>
            </div>
          </div>
          <div class="jifang-info-item" @click="openModal('area')">
            <img src="@assets/images/jifang-info-item3.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">117.67</span>
              <span class="jifang-info-item-value-unit">㎡</span>
            </div>
            <div class="jifang-info-item-title">
              <span>机房面积</span>
            </div>
          </div>
        </div>
      </div>
      <div class="container-item">
        <div class="content-header">资源池</div>
        <div class="content-body">
          <!-- 资源池网格容器 -->
          <div class="resource-pool-grid">
            <!-- 计算资源 -->
            <div class="resource-pool-item">
              <div class="resource-title-bg">
                <span class="resource-title">计算资源</span>
              </div>
              <div class="resource-stats">
                <span class="resource-value">{{ computeResourceData?.used || 203 }}/{{ computeResourceData?.total || 256 }}</span>
                <span class="resource-unit">GHZ</span>
              </div>
              <div class="resource-chart-container">
                <div class="chart-bg">
                  <div class="circle-progress" id="computeChart">
                  </div>
                </div>
                <div class="chart-label">使用率</div>
              </div>
            </div>

            <!-- 存储资源 -->
            <div class="resource-pool-item">
              <div class="resource-title-bg">
                <span class="resource-title">存储资源</span>
              </div>
              <div class="resource-stats">
                <span class="resource-value">{{ storageResourceData?.used || 20 }}/{{ storageResourceData?.total || 56 }}</span>
                <span class="resource-unit">TB</span>
              </div>
              <div class="resource-chart-container">
                <div class="chart-bg">
                  <div class="circle-progress" id="storageChart">
                    
                  </div>
                </div>
                <div class="chart-label">使用率</div>
              </div>
            </div>

            <!-- 备份资源 -->
            <div class="resource-pool-item">
              <div class="resource-title-bg">
                <span class="resource-title">备份资源</span>
              </div>
              <div class="resource-stats">
                <span class="resource-value">{{ backupResourceData?.used || 32 }}/{{ backupResourceData?.total || 86 }}</span>
                <span class="resource-unit">TB</span>
              </div>
              <div class="resource-chart-container">
                <div class="chart-bg">
                  <div class="circle-progress" id="backupChart">
                   
                  </div>
                </div>
                <div class="chart-label">占用/总数</div>
              </div>
            </div>

            <!-- 虚拟机容量 -->
            <div class="resource-pool-item">
              <div class="resource-title-bg">
                <span class="resource-title">虚拟机容量</span>
              </div>
              <div class="resource-stats">
                <span class="resource-value">{{ vmResourceData?.used || 32 }}/{{ vmResourceData?.total || 86 }}</span>
                <span class="resource-unit"></span>
              </div>
              <div class="resource-chart-container">
                <div class="chart-bg">
                  <div class="circle-progress" id="vmChart">
                  </div>
                </div>
                <div class="chart-label">占用/虚拟机总数</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="right-content">
    <div class="container right-container">
      <div class="container-item">
        <div class="device-title-wrapper">
              <div class="device-title">环境检测系统</div>
        </div>
        <!-- <div class="content-header">
          动环监测 -->
          <!-- <div class="arrow-right-box">
            <CustomSelect 
              v-model="selectedRoom" 
              :options="roomOptions" 
              @change="handleRoomChange"
            />
          </div> -->
        <!-- </div> -->
        <div class="content-body donghuan-content">

          <!-- 环境监测系统 -->
          <div class="env-monitoring">
            <div class="env-cards left-cards">
              <div class="env-card">
               
                <div class="env-info">
                  <div class="env-title">{{ envMonitoringData.avgTemp }}℃</div>
                  <img src="@assets/images/dashborad/wendu.png" alt="">
                  <div class="env-value">平均温度</div>
                </div>
              </div>
              <div class="env-card">
                
                <div class="env-info">
                  <div class="env-title">{{ envMonitoringData.avgHumidity }}RH%</div>
                  <img src="@assets/images/dashborad/shidu.png" alt="">
                  <div class="env-value">平均湿度</div>
                </div>
              </div>
              <div class="env-card">
                
                <div class="env-info">
                  <div class="env-title">{{ envMonitoringData.maxTemp }}℃</div>
                  <img src="@assets/images/dashborad/wendu.png" alt="">
                  <div class="env-value">最高温度</div>
                </div>
              </div>
              <div class="env-card">
                
                <div class="env-info">
                  <div class="env-title">{{ envMonitoringData.maxHumidity }}RH%</div>
                  <img src="@assets/images/dashborad/shidu.png" alt="">
                  <div class="env-value">最高湿度</div>
                </div>
              </div>
            </div>
            <div class="env-chart right-chart" ref="envChartRef"></div>  <!-- 新增 ECharts 温度计容器 -->
          </div>

          <!-- 设备情况 -->
          <div class="device-section">
            <div class="device-title-wrapper">
              <div class="device-title">实时视频监控</div>
              <div class="camera-switch-btn" @click="switchCameraGroup">切换</div>
            </div>
            <div class="video-monitors">
              <div class="video-row">
                <div class="video-item">
                  <TpCellPlayer :cell="camera1Cell" v-if="camera1Url" />
                  <div v-else-if="isLoadingCamera1" class="video-placeholder loading">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                  </div>
                  <div v-else class="video-placeholder">摄像头1</div>
                </div>
              </div>
              <div class="video-row">
                <div class="video-item">
                  <TpCellPlayer :cell="camera2Cell" v-if="camera2Url" />
                  <div v-else-if="isLoadingCamera2" class="video-placeholder loading">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                  </div>
                  <div v-else class="video-placeholder">摄像头2</div>
                </div>
              </div>
            </div>
          </div>

          <!-- UPS信息 -->
          <div class="ups-section">
            <div class="ups-header">
              <div class="ups-title">UPS信息</div>
              <CustomSelect 
                v-model="selectedUps" 
                :options="upsOptions" 
                @change="selectUps"
              />
              <!-- <div class="ups-selector">
                <div class="selector-item" :class="{ active: selectedUps === 'ups1' }" @click="selectUps('ups1')">UPS1</div>
                <div class="selector-item" :class="{ active: selectedUps === 'ups2' }" @click="selectUps('ups2')">UPS2</div>
              </div> -->
            </div>
            <div class="ups-content">
              <div class="ups-data-row">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1367.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-value">{{ currentUpsData.status }}</div>
                  <div class="ups-label">运行状态</div>
                </div>
              </div>
              <div class="ups-data-row elevated">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1368.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-value">{{ currentUpsData.dischargeTime }}</div>
                  <div class="ups-label">放电时长</div>
                </div>
              </div>
              <div class="ups-data-row">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1369.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-value">{{ currentUpsData.backupDuration }}</div>
                  <div class="ups-label">备用时长</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="bottom-alarms">
    <div class="alarm-item" v-for="alarm in alarmStats" :key="alarm.label">
      <img :src="alarm.img" alt="" class="alarm-bg">
      <div class="alarm-content">
        <div class="alarm-label">{{ alarm.label }}</div>
        <div class="alarm-value" :style="{ color: alarm.valueColor }">{{ alarm.value }}{{ alarm.unit }}</div>
      </div>
    </div>
  </div>

  <!-- Modal -->
  <div v-if="isModalVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
    <div class="modal-header">
      <span class="modal-title">{{ modalTitle }}</span>
      <img src="@/assets/images/close.png" alt="" srcset="" class="close-btn" @click="closeModal">
    </div>
    <div class="modal-body">
      <img v-if="modalType === 'image'" :src="modalImageSrc" alt="Modal Image" class="modal-image">
      <div v-if="modalType === 'table'" class="modal-table">
        <div class="table-header">
          <div class="th">设备名称</div>
          <div class="th">设备类型</div>
          <div class="th">在线状态</div>
          <div class="th">告警状态</div>
          <div class="th">所属机柜</div>
        </div>
        <div class="table-body">
          <div v-if="!isLoadingDeviceList && deviceList.length > 0">
            <div class="tr" v-for="item in deviceList" :key="item.deviceId">
              <div class="td">{{ item.deviceName }}</div>
              <div class="td">{{ item.deviceType === '1' ? '网络设备' : '主机' }}</div>
              <div class="td">{{ getOnlineStatusText(item.device_online) }}</div>
              <div class="td">{{ getAlarmStatusText(item.alarmStatus) }}</div>
              <div class="td">{{ item.cabinetId ? `机柜${item.cabinetId}` : 'N/A' }}</div>
            </div>
          </div>
          <div v-else class="loading-text">
            {{ isLoadingDeviceList ? '加载中...' : '暂无数据' }}
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

import LineChart from '@components/LineChart.vue'
import CustomSelect from '@components/CustomSelect.vue'
import TemperatureGauge from '@components/TemperatureGauge.vue'
import Pie3DChart from '@components/Pie3DChart.vue'
import TpCellPlayer from '@components/TpCellPlayer.vue'
import { get } from '@/api/request.js'
import * as echarts from 'echarts'
import statImg1 from '@assets/images/dashborad/Group 1335.png'
import statImg2 from '@assets/images/dashborad/Group 1335 (1).png'
import statImg3 from '@assets/images/dashborad/Group 1335 (2).png'
import statImg4 from '@assets/images/dashborad/Group 1335 (3).png'
import marketPowerImg from '@assets/images/dashborad/市电总功率.png'
import itPowerImg from '@assets/images/dashborad/IT功率.png'
import coolingEfficiencyImg from '@assets/images/dashborad/制冷效率.png'
import pueImg from '@assets/images/dashborad/PUE.png'
import outputLoadImg from '@assets/images/dashborad/输出负载.png'
import cabinetImage from '@assets/images/dashborad/cabinet.png'
import areaImage from '@assets/images/dashborad/平面图.png'

const router = useRouter()

const alarmStats = ref([
  {
    img: marketPowerImg,
    label: '市电总功率',
    value: '125',
    unit: 'KW',
    valueColor: '#FFFFFF'
  },
  {
    img: itPowerImg,
    label: 'IT功率',
    value: '89',
    unit: 'KW',
    valueColor: '#27A6FF'
  },
  {
    img: coolingEfficiencyImg,
    label: '制冷效率',
    value: '85',
    unit: '%',
    valueColor: '#00FF88'
  },
  {
    img: pueImg,
    label: 'PUE',
    value: '1.4',
    unit: '',
    valueColor: '#FFB800'
  },
  {
    img: outputLoadImg,
    label: '输出负载',
    value: '72',
    unit: '%',
    valueColor: '#FF6B6B'
  }
])

// 计算资源数据
const computeResourceData = ref({
  used: 203,
  total: 256,
  usage: 79 // 使用率百分比
})

// 存储资源数据
const storageResourceData = ref({
  used: 20,
  total: 56,
  usage: 36 // 使用率百分比
})

// 备份资源数据
const backupResourceData = ref({
  used: 32,
  total: 86,
  usage: 37 // 占用百分比
})

// 虚拟机容量数据
const vmResourceData = ref({
  used: 32,
  total: 86,
  usage: 37 // 占用百分比
})

// Modal state
const isModalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('') // 'image' or 'table'
const modalImageSrc = ref('')
const deviceList = ref([])
const isLoadingDeviceList = ref(false)

const openModal = async (type) => {
  if (type === 'cabinet') {
    modalTitle.value = '机柜总数'
    modalType.value = 'image'
    modalImageSrc.value = cabinetImage
  } else if (type === 'area') {
    modalTitle.value = '机房面积'
    modalType.value = 'image'
    modalImageSrc.value = areaImage
  } else if (type === 'device') {
    modalTitle.value = '设备总数'
    modalType.value = 'table'
    await fetchJgquipmentList()
  }
  isModalVisible.value = true
}

const closeModal = () => {
  isModalVisible.value = false
  // Reset states
  modalTitle.value = ''
  modalType.value = ''
  modalImageSrc.value = ''
  deviceList.value = []
}

/**
 * 获取设备列表数据
 * @description 从API获取机房设备总数列表，用于设备总数模态框显示
 */
const fetchJgquipmentList = async () => {
  isLoadingDeviceList.value = true
  try {
    console.log('正在获取设备列表数据...')
    const response = await fetch('http://*************:8099/api/zjRotatingRing/jgquipmentList')
    const result = await response.json()
    if (result.code === 0 && Array.isArray(result.data)) {
      deviceList.value = result.data
      console.log('设备列表数据获取成功，共', result.data.length, '条设备')
      // 调试日志：输出前3条数据的状态字段
      if (result.data.length > 0) {
        const sampleData = result.data.slice(0, 3).map(item => ({
          deviceName: item.deviceName,
          device_online: item.device_online,
          alarmStatus: item.alarmStatus,
          device_online_type: typeof item.device_online,
          alarmStatus_type: typeof item.alarmStatus
        }))
        console.log('设备状态字段示例数据:', sampleData)
      }
    } else {
      console.error('获取设备列表失败:', result.msg)
      deviceList.value = []
    }
  } catch (error) {
    console.error('请求设备列表失败:', error)
    deviceList.value = []
  } finally {
    isLoadingDeviceList.value = false
  }
}

/**
 * 获取在线状态显示文本
 * @param {number|string} status - 在线状态值：0-在线，1-离线
 * @returns {string} 状态显示文本
 * @description 处理设备在线状态显示逻辑，支持数字和字符串类型
 */
const getOnlineStatusText = (status) => {
  // 转换为数字进行比较，确保类型统一
  const numStatus = Number(status)
  if (numStatus === 0) {
    return '在线'
  } else if (numStatus === 1) {
    return '离线'
  } else {
    console.warn('未知的在线状态值:', status, '类型:', typeof status)
    return '未知'
  }
}

/**
 * 获取告警状态显示文本
 * @param {number|string} status - 告警状态值：0-无报警，1-报警
 * @returns {string} 状态显示文本
 * @description 处理设备告警状态显示逻辑，支持数字和字符串类型
 */
const getAlarmStatusText = (status) => {
  // 转换为数字进行比较，确保类型统一
  const numStatus = Number(status)
  if (numStatus === 0) {
    return '正常'
  } else if (numStatus === 1) {
    return '告警'
  } else {
    console.warn('未知的告警状态值:', status, '类型:', typeof status)
    return '未知'
  }
}

// 初始化资源池环形图（根据使用率动态调整颜色）
const initResourceRingChart = (chartId, seriesData, total, title, position = { left: '50%', top: '0%', centerX: '20%', centerY: '0%' }) => {
  const chartDom = document.getElementById(chartId);
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 根据使用率判断颜色方案
  const isWarning = seriesData > 80;
  
  // 定义颜色方案
  const colors = {
    normal: {
      primary: '#0085FF',        // 正常状态主色（蓝色）
      background: 'rgba(0, 224, 255, .15)',  // 正常状态背景色
      text: '#0085FF'            // 正常状态文字色
    },
    warning: {
      primary: {                 // 警告状态渐变色（红黄渐变）
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#FF6B35' },     // 橙红色
          { offset: 0.5, color: '#F7931E' },   // 橙色  
          { offset: 1, color: '#FFD23F' }      // 黄色
        ]
      },
      background: 'rgba(255, 107, 53, .15)', // 警告状态背景色
      text: '#FF6B35'            // 警告状态文字色
    }
  };

  const currentColors = isWarning ? colors.warning : colors.normal;

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      show: false
    },
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: seriesData + '%',
          fontSize: 28,
          fontWeight: 700,
          fontFamily: 'DIN Alternate',
          fill: currentColors.text,
          textAlign: 'center',
          textVerticalAlign: 'middle'
        }
      }
    ],
    series: [
      {
        name: 'Progress',
        type: 'pie',
        radius: ['70%', '95%'],
        center: [position.centerX || '50%', position.centerY || '15%'],
        startAngle: -127,
        data: [
          {
            value: seriesData,
            name: '',
            itemStyle: {
              color: currentColors.primary,
              borderRadius: 0,
              borderWidth: 0
            }
          },
          {
            value: (total - seriesData),
            name: '',
            emphasis: {
              disabled: true,
              label: {
                show: false
              }
            },
            itemStyle: {
              color: currentColors.background,
              borderRadius: 0,
              borderWidth: 0
            },
            tooltip: {
              show: false
            }
          },
          {
            value: (total / 0.8) * 0.2,
            itemStyle: {
              color: 'transparent',
              decal: {
                symbol: 'none'
              }
            },
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            tooltip: {
              show: false
            }
          }
        ],
        emphasis: {
          disabled: true
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  myChart.setOption(option);

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    myChart.resize();
  });

  // 如果是警告状态，在控制台输出提醒
  if (isWarning) {
    console.warn(`⚠️ 资源警告: ${title} 使用率为 ${seriesData}%，已超过80%阈值`);
  }
};

// 初始化资源池图表
const initResourcePoolCharts = () => {
  // 确保数据已初始化
  if (!computeResourceData.value) {
    console.warn('计算资源数据未初始化，跳过图表初始化')
    return
  }

  // 初始化计算资源图表
  initResourceRingChart('computeChart', computeResourceData.value.usage || 79, 100, '计算资源使用率', {
    left: '50%',
    top: '15%',
    centerX: '50%',
    centerY: '55%'
  });

  // 初始化存储资源图表
  initResourceRingChart('storageChart', storageResourceData.value?.usage || 36, 100, '存储资源使用率', {
    left: '50%',
    top: '15%',
    centerX: '50%',
    centerY: '55%'
  });

  // 初始化备份资源图表
  initResourceRingChart('backupChart', backupResourceData.value?.usage || 37, 100, '备份资源占用', {
    left: '50%',
    top: '5%',
    centerX: '50%',
    centerY: '55%'
  });

  // 初始化虚拟机容量图表
  initResourceRingChart('vmChart', vmResourceData.value?.usage || 37, 100, '虚拟机容量占用', {
    left: '50%',
    top: '15%',
    centerX: '50%',
    centerY: '55%'
  });
};

// 容量数据
const cpuUsed = ref(0)
const cpuTotal = ref(0)
const storageUsed = ref(0)
const storageTotal = ref(0)

// 设备状态数据
const deviceStatus = ref({
  normal: 118,
  main: 12,
  warning: 6,
  urgent: 0
})

// 饼图数据
const pieChartData = computed(() => [
  { name: '正常设备', value: deviceStatus.value.normal, color: '#218CDF' },
  { name: '主要设备', value: deviceStatus.value.main, color: '#09D8DB' },
  { name: '警告设备', value: deviceStatus.value.warning, color: '#FCB24A' },
  { name: '紧急设备', value: deviceStatus.value.urgent, color: '#FF744C' }
])

// 温度监测数据
const temperatureData = ref([
  { time: '3:00', temperature: 15 },
  { time: '6:00', temperature: 25 },
  { time: '9:00', temperature: 24 },
  { time: '12:00', temperature: 12 },
  { time: '15:00', temperature: 28 },
  { time: '18:00', temperature: 30 },
  { time: '21:00', temperature: 24 }
])

// 温度监测接口数据
const maxTemperature = ref('--') // 最高温度
const minTemperature = ref('--') // 最低温度
const abnormalDeviceCount = ref(0) // 异常设备数
const temperatureLineData = ref([]) // 温度趋势线数据
const isLoadingTempData = ref(false) // 加载状态

// 动环监测数据
const envMonitoringData = ref({
  abnormalDeviceCount: 0,
  minTemp: '--',
  avgHumidity: '--',
  avgTemp: '--',
  maxTemp: '--',
  avgPm25: '--',
  maxHumidity: '--'  // 新增最高湿度默认值
})

// 机房选择相关
const selectedRoom = ref('A机房')
const roomOptions = ref([
  { value: 'A机房', label: 'A机房' },
  { value: 'B机房', label: 'B机房' },
  { value: 'C机房', label: 'C机房' },
  { value: 'D机房', label: 'D机房' }
])

// 设备选择状态
const selectedDevices = ref(new Set(['摄像头', '列间空调', '机房空调', '配电柜']))

// 设备类型映射
const deviceTypeMap = {
  '摄像头': '摄像头',
  '门禁': '门禁',
  '精密列头柜': '精密列头柜',
  '列间空调': '列间空调',
  '机房空调': '机房空调',
  '配电柜': '配电柜',
  '温湿度设备': '温湿度设备',
  '漏水': '漏水',
  '烟感': '烟感'
}

// UPS相关数据
const selectedUps = ref('ups1') // 当前选中的UPS
const upsOptions = ref([
  { value: 'ups1', label: 'UPS1' },
  { value: 'ups2', label: 'UPS2' }
])
const upsDevicesData = ref({
  ups1: {
    status: '正常',
    dischargeTime: '2.5h',
    backupDuration: '24h'
  },
  ups2: {
    status: '正常',
    dischargeTime: '3.2h',
    backupDuration: '24h'
  }
})

// 当前显示的UPS数据
const currentUpsData = ref(upsDevicesData.value.ups1)

// 摄像头相关数据
const currentCameraGroup = ref(1) // 当前摄像头组：1或2

// 摄像头API URL映射配置
const cameraApiMappings = {
  1: {
    camera1: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=54925bf108de411698d05ad3e27cdc54', // 机房大门
    camera2: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=31a74893a08541c3ad553523888ba684'  // UPS配电间
  },
  2: {
    camera1: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=bd50ec1a9cc9411d9d0899d725c941a6', // 微模块通道前
    camera2: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=cb2b6d5aaa1e471d82785ee8ff1c0d2a'  // 微模块通道后
  },
  3: {
    camera1: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=90bcb7ed51a346969782be5b970bb3c1', // 微模块通道前
    camera2: 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=e2c8dfc3692d41fea60af719f7193c0c'  // 微模块通道后
  }
}

// 当前显示的摄像头URL
const camera1Url = ref('')
const camera2Url = ref('')
const isLoadingCamera1 = ref(false)
const isLoadingCamera2 = ref(false)

// 创建稳定的摄像头数据对象，避免每次渲染都创建新对象
const camera1Cell = computed(() => ({
  url: camera1Url.value
}))

const camera2Cell = computed(() => ({
  url: camera2Url.value
}))

// 获取真实视频流URL
const fetchVideoStreamUrl = async (apiUrl) => {
  try {
    console.log('正在获取视频流URL:', apiUrl)
    
    const response = await fetch(apiUrl, {
      headers: {
        'tenant-id': '1'
      }
    })

    if (!response.ok) {
      throw new Error(`请求视频流URL失败: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.code !== 0) {
      throw new Error(result.msg || '获取视频流URL时发生错误')
    }

    console.log('获取到视频流URL:', result.data)
    return result.data
  } catch (error) {
    console.error('获取视频流URL失败:', error)
    return null
  }
}

// 处理设备选择
const handleDeviceSelect = (deviceType) => {
  console.log('用户点击设备:', deviceType)

  if (selectedDevices.value.has(deviceType)) {
    selectedDevices.value.delete(deviceType)
    console.log('取消选择设备:', deviceType)
  } else {
    selectedDevices.value.add(deviceType)
    console.log('选择设备:', deviceType)
  }

  console.log('当前选中的设备:', Array.from(selectedDevices.value))

  // 触发设备显示/隐藏逻辑
  updateDeviceVisibility()
}

// 更新设备可见性
const updateDeviceVisibility = () => {
  // 通过事件通知ThreeScene组件更新设备显示
  window.dispatchEvent(new CustomEvent('deviceSelectionChanged', {
    detail: {
      selectedDevices: Array.from(selectedDevices.value),
      showAll: selectedDevices.value.size === 0
    }
  }))
}

// 处理机房切换
const handleRoomChange = (value) => {
  console.log('切换到机房:', value)
  // 这里可以添加切换机房的逻辑，比如更新动环监测数据等
}

// 获取资源池信息
const fetchResourcePoolInfo = async () => {
  try {
    console.log('正在获取资源池数据')
    const response = await fetch('http://*************:8099/api/zjRotatingRing/resourcePoolInfo')
    const result = await response.json()
    
    if (result.code === 0 && Array.isArray(result.data)) {
      const dataArray = result.data
      
      // 遍历数组，根据name字段匹配对应的资源类型
      dataArray.forEach(item => {
        const used = parseFloat(item.used) || 0
        const total = parseFloat(item.total) || 1
        const rate = parseFloat(item.rate) || 0
        
        // 可以使用API返回的rate，或者自己计算
        const usage = rate || Math.round((used / total) * 100)
        
        const resourceData = {
          used: used,
          total: total,
          usage: usage
        }
        
        // 根据name字段匹配对应的资源类型
        switch (item.name) {
          case '计算资源':
            computeResourceData.value = resourceData
            console.log('计算资源数据更新:', resourceData)
            break
          case '存储资源':
            storageResourceData.value = resourceData
            console.log('存储资源数据更新:', resourceData)
            break
          case '备份资源':
            backupResourceData.value = resourceData
            console.log('备份资源数据更新:', resourceData)
            break
          case '虚拟机容量':
            vmResourceData.value = resourceData
            console.log('虚拟机容量数据更新:', resourceData)
            break
          default:
            console.warn('未识别的资源类型:', item.name)
        }
      })
      
      console.log('资源池数据获取成功:', {
        compute: computeResourceData.value,
        storage: storageResourceData.value,
        backup: backupResourceData.value,
        vm: vmResourceData.value
      })
      
      // 数据更新后重新初始化图表
      setTimeout(() => {
        initResourcePoolCharts()
      }, 100)
      
      return true
    } else {
      console.error('获取资源池数据失败:', result.msg || '数据格式错误')
      return false
    }
  } catch (error) {
    console.error('请求资源池数据失败:', error)
    return false
  }
}

// 获取UPS主要信息
const fetchUpsMainInfo = async (upsName) => {
  try {
    console.log('正在获取UPS数据:', upsName)
    const response = await fetch(`http://*************:8099/api/zjRotatingRing/upsMainInfo?name=${upsName}`)
    const result = await response.json()
    
    if (result.code === 0 && result.data) {
      const data = result.data
      
      // 处理UPS数据，根据真实API数据格式
      const upsData = {
        status: data.status || '正常',
        dischargeTime: data.fdtime ? `${data.fdtime}h` : '2.5h',
        backupDuration: data.bytime ? `${data.bytime}h` : '24h'
      }
      
      console.log('UPS数据获取成功:', {
        原始数据: data,
        处理后数据: upsData
      })
      return upsData
    } else {
      console.error('获取UPS数据失败:', result.msg)
      return null
    }
  } catch (error) {
    console.error('请求UPS数据失败:', error)
    return null
  }
}

// 选择UPS设备
const selectUps = async (upsType) => {
  selectedUps.value = upsType
  
  // 根据UPS类型确定API参数
  const upsName = upsType === 'ups1' ? 'UPS1' : 'UPS2'
  
  // 获取真实的UPS数据
  const upsData = await fetchUpsMainInfo(upsName)
  
  if (upsData) {
    // 更新UPS数据
    upsDevicesData.value[upsType] = upsData
    currentUpsData.value = upsData
  } else {
    // 如果API失败，使用本地缓存数据
    currentUpsData.value = upsDevicesData.value[upsType]
  }
  
  console.log('切换到UPS:', upsType, currentUpsData.value)
}

// 切换摄像头组
const switchCameraGroup = () => {
  currentCameraGroup.value = currentCameraGroup.value % 3 + 1
  updateCameraUrls()
  console.log('切换到摄像头组:', currentCameraGroup.value)
}

// 更新摄像头URL
const updateCameraUrls = async () => {
  const apiUrls = cameraApiMappings[currentCameraGroup.value]
  if (apiUrls) {
    console.log('开始获取摄像头组', currentCameraGroup.value, '的视频流URL')
    
    // 重置URL，显示加载状态
    camera1Url.value = ''
    camera2Url.value = ''
    isLoadingCamera1.value = true
    isLoadingCamera2.value = true
    
    // 并行获取两个摄像头的URL
    const [camera1StreamUrl, camera2StreamUrl] = await Promise.all([
      fetchVideoStreamUrl(apiUrls.camera1),
      fetchVideoStreamUrl(apiUrls.camera2)
    ])
    
    // 设置获取到的真实视频流URL
    if (camera1StreamUrl) {
      camera1Url.value = camera1StreamUrl
    }
    if (camera2StreamUrl) {
      camera2Url.value = camera2StreamUrl
    }
    
    isLoadingCamera1.value = false
    isLoadingCamera2.value = false
    
    console.log('摄像头URL已更新:', {
      group: currentCameraGroup.value,
      camera1: camera1Url.value,
      camera2: camera2Url.value
    })
  }
}

// 初始化摄像头URL
const initCameraUrls = () => {
  updateCameraUrls()
}

// 获取设备列表数据
const fetchDeviceList = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/devicelist?remark=A')
    const result = await response.json()

    if (result.code === 0 && Array.isArray(result.data)) {
      const devices = result.data

      // 处理动环监测数据
      processEnvironmentData(devices)

      console.log('设备数据加载成功:', {
        envData: envMonitoringData.value
      })
    } else {
      console.error('获取设备列表失败:', result.msg)
    }
  } catch (directError) {
    console.error('直接请求失败:', directError)
  }
}



// 处理动环监测数据
const processEnvironmentData = (devices) => {
  const temperatures = []
  const humidities = []
  let pm25Value = '--'

  devices.forEach(device => {
    if (!device.canshu || !Array.isArray(device.canshu)) return

    device.canshu.forEach(param => {
      const value = parseFloat(param.currentValue)
      if (isNaN(value)) return

      // 收集温度数据
      if (param.signalName.includes('温度') && (param.unit === '°C' || param.unit === '℃')) {
        temperatures.push(value)
      }

      // 收集湿度数据
      if (param.signalName.includes('湿度') && (param.unit === '%' || param.unit === 'RH%')) {
        humidities.push(value)
      }

      // 如果有PM2.5数据（虽然当前数据中没有，但保留逻辑）
      if (param.signalName.includes('PM2.5') || param.signalName.includes('pm2.5')) {
        pm25Value = value
      }
    })
  })

  // 计算统计值
  const parseValue = (val) => {
    return isNaN(val) ? '--' : Number(val.toFixed(1))
  }

  if (temperatures.length > 0) {
    const minTemp = Math.min(...temperatures)
    const maxTemp = Math.max(...temperatures)
    const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length

    envMonitoringData.value.minTemp = parseValue(minTemp)
    envMonitoringData.value.maxTemp = parseValue(maxTemp)
    envMonitoringData.value.avgTemp = parseValue(avgTemp)

    // 同步更新最高最低温度显示
    maxTemperature.value = parseValue(maxTemp)
    minTemperature.value = parseValue(minTemp)
  }

  if (humidities.length > 0) {
    const avgHumidity = humidities.reduce((sum, humidity) => sum + humidity, 0) / humidities.length
    envMonitoringData.value.avgHumidity = parseValue(avgHumidity)
    const maxHum = Math.max(...humidities)
    envMonitoringData.value.maxHumidity = parseValue(maxHum)  // 新增最高湿度计算
  }

  // PM2.5暂时使用默认值（数据中没有）
  envMonitoringData.value.avgPm25 = pm25Value === '--' ? '12' : parseValue(pm25Value)

  // 异常设备数量暂时使用默认值
  envMonitoringData.value.abnormalDeviceCount = 0

  console.log('动环监测数据处理完成:', {
    temperatures,
    humidities,
    result: envMonitoringData.value
  })

  // 数据更新后重新渲染温度计
  setTimeout(() => {
    initEnvChart()
  }, 100)
}

// 获取动环监测数据
const fetchEnvMonitoringData = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/rotatingRingMonitor')
    const result = await response.json()
    if (result.code === 0 && result.data) {
      const data = result.data
      const parseValue = (str) => {
        if (typeof str !== 'string') return str
        const val = parseFloat(str)
        return isNaN(val) ? '--' : val
      }
      envMonitoringData.value = {
        abnormalDeviceCount: parseValue(data.ycsb),
        minTemp: parseValue(data.zxwd),
        avgHumidity: parseValue(data.sdavg),
        avgTemp: parseValue(data.wdavg),
        maxTemp: parseValue(data.zgwd),
        avgPm25: parseValue(data.pmavg),
        maxHumidity: parseValue(data.sdmax) || '--'  // 新增最高湿度
      }

      // 同步更新最高最低温度显示
      if (maxTemperature.value === '--' && envMonitoringData.value.maxTemp !== '--') {
        maxTemperature.value = envMonitoringData.value.maxTemp
      }
      if (minTemperature.value === '--' && envMonitoringData.value.minTemp !== '--') {
        minTemperature.value = envMonitoringData.value.minTemp
      }

      console.log('动环监测数据加载成功（直接请求）:', envMonitoringData.value)

      // 数据更新后重新渲染温度计
      setTimeout(() => {
        initEnvChart()
      }, 100)
    } else {
      console.error('获取动环监测数据失败:', result.msg)
    }
  } catch (directError) {
    console.error('直接请求也失败:', directError)
  }
}

// 获取设备容量数据
const fetchDeviceCapacity = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/equipmentCapacity')
    const result = await response.json()
    if (result.code === 0 && result.data) {
      const total = parseFloat(result.data.cpuMb)
      const used = parseFloat(result.data.cpuUsed)

      if (!isNaN(total) && !isNaN(used)) {
        cpuTotal.value = total
        cpuUsed.value = used
      }

      const sTotal = parseFloat(result.data.storageMb)
      const sUsed = parseFloat(result.data.storageUsed)
      if (!isNaN(sTotal) && !isNaN(sUsed)) {
        storageTotal.value = sTotal
        storageUsed.value = sUsed
      }

    } else {
      console.error('获取设备容量数据失败:', result.msg)
    }
  } catch (error) {
    console.error('请求设备容量数据失败:', error)
  }
}

// 获取功率统计数据
const fetchPowerStats = async () => {
  try {
    console.log('获取功率统计数据')

    // 尝试调用真实API，如果失败则使用模拟数据
    try {
      const response = await fetch('http://*************:8099/api/zjRotatingRing/powerStatistics')
      const result = await response.json()

      if (result.code === 0 && result.data) {
        const data = result.data
        alarmStats.value[0].value = data.marketPower?.toString() || '125'
        alarmStats.value[1].value = data.itPower?.toString() || '89'
        alarmStats.value[2].value = data.coolingEfficiency?.toString() || '85'
        alarmStats.value[3].value = data.pue?.toString() || '1.4'
        alarmStats.value[4].value = data.outputLoad?.toString() || '72'
        console.log('功率统计数据加载成功（API）:', alarmStats.value)
        return
      }
    } catch (apiError) {
      console.log('API调用失败，使用模拟数据:', apiError.message)
    }

    // 使用模拟数据作为fallback
    const mockData = {
      marketPower: 125,    // 市电总功率
      itPower: 89,         // IT功率
      coolingEfficiency: 85, // 制冷效率
      pue: 1.4,            // PUE
      outputLoad: 72       // 输出负载
    }

    // 更新数据
    alarmStats.value[0].value = mockData.marketPower.toString()
    alarmStats.value[1].value = mockData.itPower.toString()
    alarmStats.value[2].value = mockData.coolingEfficiency.toString()
    alarmStats.value[3].value = mockData.pue.toString()
    alarmStats.value[4].value = mockData.outputLoad.toString()

    console.log('功率统计数据加载成功（模拟）:', alarmStats.value)

  } catch (error) {
    console.error('请求功率统计数据失败:', error)
  }
}

// 获取温度趋势统计数据
const fetchMaxTempStatistics = async () => {
  try {
    isLoadingTempData.value = true
    const response = await fetch('http://*************:8099/api/zjRotatingRing/maxTempStatistics')
    const result = await response.json()

    if (result.code === 0) {
      const data = result.data
      
      // 只在设备列表数据还没有加载时才使用这里的温度数据作为fallback
      if (maxTemperature.value === '--' && data.zgwd) {
        maxTemperature.value = data.zgwd
      }
      if (minTemperature.value === '--' && data.zxwd) {
        minTemperature.value = data.zxwd
      }
      
      abnormalDeviceCount.value = data.ycsb || 0

      // 处理温度趋势线数据
      if (data.wdLine && Array.isArray(data.wdLine)) {
        temperatureLineData.value = data.wdLine.map(item => ({
          time: `${item.hour}:00`,
          temperature: parseFloat(item.zgwd) || 0
        }))
        temperatureData.value = temperatureLineData.value
      }

      console.log('温度趋势数据加载成功:', {
        maxTemp: maxTemperature.value,
        minTemp: minTemperature.value,
        abnormalCount: abnormalDeviceCount.value,
        lineData: temperatureLineData.value
      })
    } else {
      console.error('获取温度统计数据失败:', result.msg)
    }
  } catch (directError) {
    console.error('获取温度趋势数据失败:', directError)
  } finally {
    isLoadingTempData.value = false
  }
}

// 监听3D模型加载完成事件
const handleModelLoaded = () => {
  console.log('3D模型加载完成，应用默认设备选择')
  console.log('当前选中的设备:', Array.from(selectedDevices.value))
  // 延迟一小段时间确保3D场景完全初始化
  setTimeout(() => {
    updateDeviceVisibility()
    console.log('已发送设备选择变化事件')
  }, 500)
}

// 初始化UPS数据
const initUpsData = async () => {
  console.log('初始化UPS数据')
  
  // 获取UPS1和UPS2的数据
  const ups1Data = await fetchUpsMainInfo('UPS1')
  const ups2Data = await fetchUpsMainInfo('UPS2')
  
  if (ups1Data) {
    upsDevicesData.value.ups1 = ups1Data
  }
  
  if (ups2Data) {
    upsDevicesData.value.ups2 = ups2Data
  }
  
  // 设置默认选中的UPS数据
  currentUpsData.value = upsDevicesData.value[selectedUps.value]
  
  console.log('UPS数据初始化完成:', {
    ups1: upsDevicesData.value.ups1,
    ups2: upsDevicesData.value.ups2,
    current: currentUpsData.value
  })
}

// 组件挂载时获取数据
onMounted(async () => {
  fetchPowerStats()
  fetchMaxTempStatistics()

  // 先获取设备数据，这会触发温度计的重新渲染
  await fetchDeviceList() // 这个函数现在包含了动环监测数据处理

  // 如果设备数据没有获取到温度数据，尝试备用API
  if (envMonitoringData.value.avgTemp === '--') {
    await fetchEnvMonitoringData()
  }

  fetchDeviceCapacity()

  // 初始化UPS数据
  initUpsData()

  // 获取资源池数据
  fetchResourcePoolInfo()

  // 初始化摄像头URL
  initCameraUrls()

  // 监听3D模型加载完成事件
  window.addEventListener('threeSceneReady', handleModelLoaded)

  // 如果此时还没有温度数据，使用默认值初始化温度计
  if (envMonitoringData.value.avgTemp === '--') {
    envMonitoringData.value.avgTemp = 25 // 设置一个合理的默认温度
    initEnvChart()
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('threeSceneReady', handleModelLoaded)
})

const envChartRef = ref(null)  // 新增容器引用

function initEnvChart() {
  const el = envChartRef.value
  if (!el) return
  const chart = echarts.init(el)
  const TP_value = Number(envMonitoringData.value.avgTemp) || 25 // 默认25度
  const kd = []
  const Gradient = []
  let showValue = ''

  console.log('温度计初始化，当前温度值:', TP_value)

  // 刻度使用柱状图模拟，短设置1，长的设置3；构造一个数据
  for (let i = 0; i <= 135; i++) {
    if (i < 10 || i > 130) {
      kd.push('')
    } else if ((i - 10) % 20 === 0) {
      kd.push('-3')
    } else if ((i - 10) % 4 === 0) {
      kd.push('-1')
    } else {
      kd.push('')
    }
  }

  // 中间线的渐变色设置
  if (TP_value > 20) {
    Gradient.push({ offset: 0, color: '#93FE94' }, { offset: 0.5, color: '#E4D225' }, { offset: 1, color: '#E01F28' })
  } else if (TP_value > -20) {
    Gradient.push({ offset: 0, color: '#93FE94' }, { offset: 1, color: '#E4D225' })
  } else {
    Gradient.push({ offset: 1, color: '#93FE94' })
  }

  if (TP_value > 62) {
    showValue = 62
  } else if (TP_value < -60) {
    showValue = -60
  } else {
    showValue = TP_value
  }

  // 由于不显示标签，不需要设置位置和颜色
  // 因为柱状初始化为0，温度存在负值，所以加上负值60和空出距离10
  const option = {
    backgroundColor: 'transparent',
    tooltip: { show: false },
    title: { text: '温度计', show: false },
    yAxis: [
      { show: false, data: [], min: 0, max: 135, axisLine: { show: false } },
      { show: false, min: 0, max: 50 },
      { type: 'category', data: ['', '', '', '', '', '', '', '', '', '', '°C'], position: 'left', offset: -60, axisLabel: { fontSize: 10, color: 'white' }, axisLine: { show: false }, axisTick: { show: false } }
    ],
    xAxis: [
      { show: false, min: -10, max: 80, data: [] },
      { show: false, min: -10, max: 80, data: [] },
      { show: false, min: -10, max: 80, data: [] },
      { show: false, min: -5, max: 80 }
    ],
    series: [
      {
        name: '条',
        type: 'bar',
        // 对应上面XAxis的第一个对象配置
        xAxisIndex: 0,
        data: [
          {
            value: (showValue + 70), // 参考示例的计算方式
            label: {
              normal: {
                show: false // 隐藏右边的文字显示
              }
            }
          }
        ],
        barWidth: 7, // 减小40%：12 * 0.6 = 7.2，取整为7
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, Gradient)
          }
        },
        z: 2
      },
      {
        name: '白框',
        type: 'bar',
        xAxisIndex: 1,
        barGap: '-100%',
        data: [134],
        barWidth: 12, // 减小40%：20 * 0.6 = 12
        itemStyle: {
          normal: {
            color: '#0C2E6D',
            barBorderRadius: 50
          }
        },
        z: 1
      },
      {
        name: '外框',
        type: 'bar',
        xAxisIndex: 2,
        barGap: '-100%',
        data: [135],
        barWidth: 16, // 减小40%：26 * 0.6 = 15.6，取整为16
        itemStyle: {
          normal: {
            color: '#4577BA',
            barBorderRadius: 50
          }
        },
        z: 0
      },
      {
        name: '圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [0],
        xAxisIndex: 0,
        symbolSize: 19, // 减小40%：32 * 0.6 = 19.2，取整为19
        itemStyle: {
          normal: {
            color: '#93FE94',
            opacity: 1
          }
        },
        z: 2
      },
      {
        name: '白圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [0],
        xAxisIndex: 1,
        symbolSize: 25, // 减小40%：42 * 0.6 = 25.2，取整为25
        itemStyle: {
          normal: {
            color: '#0C2E6D',
            opacity: 1
          }
        },
        z: 1
      },
      {
        name: '外圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [0],
        xAxisIndex: 2,
        symbolSize: 29, // 减小40%：48 * 0.6 = 28.8，取整为29
        itemStyle: {
          normal: {
            color: '#4577BA',
            opacity: 1
          }
        },
        z: 0
      },
      {
        name: '刻度',
        type: 'bar',
        yAxisIndex: 0,
        xAxisIndex: 3,
        label: {
          normal: {
            show: true,
            position: 'left',
            distance: 8,
            color: 'white',
            fontSize: 10, // 适中的字体大小
            formatter: function(params) {
              if (params.dataIndex > 130 || params.dataIndex < 10) {
                return '';
              } else {
                if ((params.dataIndex - 10) % 20 === 0) {
                  return params.dataIndex - 70;
                } else {
                  return '';
                }
              }
            }
          }
        },
        barGap: '-100%',
        data: kd,
        barWidth: 1,
        itemStyle: {
          normal: {
            color: 'white',
            barBorderRadius: 120
          }
        },
        z: 0
      }
    ]
  }
  chart.setOption(option)
  window.addEventListener('resize', () => chart.resize())
}
</script>

<style scoped lang="scss">
.left-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 480px;
  height: 100%;
  background: url('@assets/images/left-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;
  z-index: 1;

  .container {
    margin-left: 17px;
  }
}

.right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 529px;
  height: 100%;
  background: url('@assets/images/right-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;
  z-index: 1;

  .container {
    margin-right: 17px;
    margin-left: auto;
  }
}

.container {
  width: 456px;
  height: 968px;
  background: linear-gradient(271deg, rgba(6, 25, 55, 0.3) 0%, rgba(9, 61, 102, 0.4) 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  // 毛玻璃效果
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  //justify-content: space-between;
}

.right-container .container-item {
  flex: 1;
}

.container-item {
  display: flex;
  flex-direction: column;
  
  &:first-child {
    flex: 0 0 auto; // 机房信息固定高度
  }
  
  &:last-child {
    flex: 1; // 资源池占用剩余空间
    
    .content-body {
      flex: 1; // content-body撑满剩余高度
      display: flex;
      flex-direction: column;
    }
  }
}

.content-header {
  width: 100%;
  height: 54px;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  line-height: 54px;
  padding-left: 34px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .arrow-right-box {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .arrow-right {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}

.jifang-info {
  width: 100%;
  padding: 0 18px 34px 18px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;

  .jifang-info-item {
    width: 123px;
    height: 235px;
    background: url('@assets/images/jifang-info-bg.png') no-repeat center center / 100% 100%;
    padding-top: 35px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.2);
    }

    .jifang-info-item-value {
      margin-top: 21px;

      .jifang-info-item-value-num {
        font-family: DIN Alternate;
        font-weight: bold;
        font-size: 22px;
        color: #FFFFFF;
        letter-spacing: 1px;
      }

      .jifang-info-item-value-unit {
        font-weight: 400;
        font-size: 12px;
        color: #9FB0D8;
      }
    }

    .jifang-info-item-title {
      font-weight: 500;
      font-size: 14px;
      margin-top: 22px;
    }
  }
}

.idc-img {
  width: 422px;
  height: 291px;
  margin-left: 17px;
  margin-top: 9px;
}

.temp-info {
  width: 100%;
  padding: 15px 29px 0 29px;
  box-sizing: border-box;

  .temp-top {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .temp-left-column {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .temp-right-space {
      width: 180px;
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .temp-top-item {
      width: 182px;
      height: 68px;
      padding: 10px 0 16px 8px;
      box-sizing: border-box;
      background: url('@assets/images/temp-bg.png') no-repeat center center / 100% 100%;
      display: flex;
      align-items: center;

      .temp-alarm-img {
        width: 42px;
        height: 42px;
        margin-right: 12px;
      }

      .temp-alarm-info {
        .temp-alarm-info-title {
          font-weight: 400;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .temp-alarm-info-value {

          .temp-alarm-info-value-num {
            font-family: DIN Alternate, DIN Alternate;
            font-weight: bold;
            font-size: 16px;
            color: #B0E0FF;
          }

          .temp-alarm-info-value-unit {
            font-weight: 400;
            font-size: 12px;
            color: #B0E0FF;
            margin-left: 5px;
          }
        }
      }

    }
  }

  .temp-line {
    margin-top: 20px;
    padding: 0 10px;
  }
}

// 动环监测样式
.donghuan-content {
  width: 100%;
  padding: 4px 13px;
  box-sizing: border-box;
}

.donghuan-top {
  display: flex;
  justify-content: space-around;
  margin-bottom: 6px;
  padding: 12px 16px;
  background: url('@assets/images/line_img_1.png') no-repeat center center / 100% 100%;
  box-sizing: border-box;

}

.donghuan-range {
  display: flex;
  align-items: center;

  .range-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;

    &.blue-dot {
      background: #40AAFC;
    }

    &.red-dot {
      background: #FD8D79;
    }
  }

  .range-text-title {
    font-weight: 500;
    font-size: 14px;
  }

  .range-text-value {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 20px;
  }

  .blue {
    color: #40AAFC;
  }

  .red {
    color: #FD8D79;
  }

  .range-text-unit {
    font-weight: 400;
    font-size: 14px;
    color: #97B0C8;
    margin-left: 7px;
  }
}

.env-cards {
  display: flex;
  justify-content: space-between;
}

.env-card {
  // width: 148px;
  height: 48px;
  // padding: 4px 0px 4px 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  text-align:center;
  margin-bottom: 14px;
  // background: url('@assets/images/env-card-bg.png') no-repeat center center / 100% 100%;

  .env-icon {
    width: 18px;
    height: 18px;
    margin-right: 23px;
  }

  .env-info {
    .env-title {
      font-weight: 400;
      font-size: 16px;
      color:#79BFFF;
    }

    .env-value {
      font-family: DIN Alternate, DIN Alternate;
      font-size: 14px;
      color: #EBF1FF;
      letter-spacing: 1px;

      &.orange {
        color: #FD8D79;
      }
    }
  }
}

.device-section {
  margin-bottom: 2px;
}

.device-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-left: 14px;
  margin-right: 14px;
  margin-top: 20px;
}

.device-title {
  position: relative;
  font-weight: 500;
  font-size: 14px;

  &::before {
    position: absolute;
    top: 8px;
    left: -14px;
    content: '';
    display: block;
    width: 6px;
    height: 4px;
    background: #27ABFF;
  }
}

.camera-switch-btn {
  padding: 6px 16px;
  background: linear-gradient(135deg, rgba(39, 171, 255, 0.2) 0%, rgba(39, 171, 255, 0.4) 100%);
  border: 1px solid rgba(39, 171, 255, 0.5);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;

  &:hover {
    background: linear-gradient(135deg, rgba(39, 171, 255, 0.4) 0%, rgba(39, 171, 255, 0.6) 100%);
    border-color: rgba(39, 171, 255, 0.8);
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 0 4px rgba(39, 171, 255, 0.2);
  }
}

.video-monitors {
  padding: 0 14px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.video-row {
  width: 100%;
}

.video-item {
  width: 100%;
  height: 220px;
  border-radius: 4px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(39, 171, 255, 0.3);
  position: relative;

  &:hover {
    border-color: rgba(39, 171, 255, 0.6);
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.3);
  }
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #9FB0D8;
  background: linear-gradient(135deg, rgba(20, 53, 78, 0.8) 0%, rgba(39, 171, 255, 0.1) 100%);
  
  &.loading {
    flex-direction: column;
    gap: 10px;
    color: #27ABFF;
    
    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(39, 171, 255, 0.2);
      border-top: 3px solid #27ABFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0px;
}

.device-item {
  width: 148px;
  height: 48px;
  padding: 4px 0px 4px 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: url('@assets/images/env-card-bg.png') no-repeat center center / 100% 100%;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    filter: brightness(1.1);
  }

  &.selected {
    background-color: rgba(39, 171, 255, 0.2);
    border: 1px solid #27ABFF;
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.4);
  }

  .device-icon {
    width: 18px;
    height: 18px;
    margin-right: 23px;
  }

  .device-info {
    .device-name {
      font-weight: 400;
      font-size: 14px;
    }

    .device-status {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 20px;
      color: #EBF1FF;
      letter-spacing: 1px;

      .orange {
        color: #FD8D79;
      }
    }
  }
}

.ups-section {
  width: 100%;
  height: 212px;

  .ups-header {
    width: 100%;
    height: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 20px;

    .ups-title {
      position: relative;
  font-weight: 500;
  font-size: 14px;

  &::before {
    position: absolute;
    top: 8px;
    left: -14px;
    content: '';
    display: block;
    width: 6px;
    height: 4px;
    background: #27ABFF;
  }
    }

    .ups-selector {
      display: flex;
      gap: 10px;

      .selector-item {
        padding: 4px 12px;
        font-size: 14px;
        color: rgba(#FFFFFF, 0.5);
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          color: #FFFFFF;
          background: rgba(#27ABFF, 0.2);
        }

        &.active {
          color: #FFFFFF;
          background: rgba(#27ABFF, 0.3);
          box-shadow: 0 0 10px rgba(#27ABFF, 0.2);
        }
      }
    }
  }

  .ups-content {
    display: flex;
    justify-content: space-around;
    padding: 0 15px;
    position: relative;
    height: 140px;
  }

  .ups-data-row {
    position: relative;
    width: 110px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;

    &.elevated {
      transform: translateY(-15px);
    }

    .ups-img-wrapper {
      width: 110px;
      height: 90px;
      position: relative;

      .ups-bg-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }

    .ups-text {
      position: absolute;
      bottom: -13px;
      left: 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
      padding: 5px 0;
    }

    .ups-label {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #93D5FF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      word-wrap: break-word;
      word-break: break-word;
      white-space: normal;
      max-width: 100%;
      line-height: 1.2;
    }

    .ups-value {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 15px;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }
}

.bottom-alarms {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px; /* 减小间距以容纳5个项目 */
  pointer-events: all;
}

.alarm-item {
  position: relative;
  width: 200px; /* 减小宽度以容纳5个项目 */
  height: 80px; /* 稍微减小高度 */
}

.alarm-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.alarm-content {
  position: absolute;
  top: 50%;
  left: 35%; /* 稍微调整位置 */
  transform: translateY(-50%);
  text-align: left;
  color: #fff;
}

.alarm-label {
  font-size: 12px; /* 稍微减小字体 */
  color: #a3c0e3;
  margin-bottom: 2px;
}

.alarm-value {
  font-size: 20px; /* 稍微减小字体以适应更小的容器 */
  font-weight: bold;
  font-family: DIN Alternate, DIN Alternate;
}

// 设备状态样式
.device-status-content {
  width: 100%;
  padding: 15px 18px;
  box-sizing: border-box;
  display: flex;
  gap: 15px;
}

.device-status-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-status-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 设备状态统计样式
.status-stats {
  margin-bottom: 12px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #C0DAEA;

  .status-dot {
    width: 6px;
    height: 6px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .status-label {
    margin-right: 8px;
    font-weight: 400;
    font-size: 12px;
    color: #A9C5C8;
  }

  .status-count {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 14px;
    color: #DAECF6;
  }

  &.green .status-dot {
    background-color: #09D8DB;
  }

  &.blue .status-dot {
    background-color: #218CDF;
  }

  &.orange .status-dot {
    background-color: #FCB24A;
  }

  &.red .status-dot {
    background-color: #FF744C;
  }
}

.chart-container {
  height: 130px;
  flex: 1;
}

// 设备状态卡片样式
.device-status-card {
  width: 100%;
  height: 48px;
  padding: 8px 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;

  &.normal-bg {
    background-image: url('@assets/images/dashborad/正常设备.png');
  }

  &.main-bg {
    background-image: url('@assets/images/dashborad/未注册设备.png');
  }

  &.warning-bg {
    background-image: url('@assets/images/dashborad/告警设备.png');
  }

  &.urgent-bg {
    background-image: url('@assets/images/dashborad/离线设备.png');
  }

  .device-status-label {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .device-status-count {
    position: absolute;
    right: 15px;
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Modal styles - 模仿ThreeScene弹窗样式
.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80vw;
  height: 80vh;
  background: #14354E;
  border-radius: 4px;
  z-index: 10002;
  overflow: hidden;
  color: #fff;
  pointer-events: auto; /* 确保弹窗自身可以接收点击事件 */
}

.modal-header {
  height: 54px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;

  .modal-title {
    margin-left: 55px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
  }

  .close-btn {
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin-right: 26px;
  }
}

.modal-body {
  overflow: hidden;
  height: calc(100% - 54px);
  padding: 0;
  box-sizing: border-box;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 20px;
  box-sizing: border-box;
}

.modal-table {
  height: 100%;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 200px 140px 120px 120px 1fr;
  padding: 20px 56px;
  box-sizing: border-box;
}

.th {
  font-weight: 500;
  font-size: 14px;
  color: #7BCCFF;
}

.table-body {
  height: calc(100% - 62px); /* 80vh total height - 54px header - 62px table-header */
  overflow-y: auto;
  padding: 0 24px;
}

.tr {
  display: grid;
  grid-template-columns: 200px 140px 120px 120px 1fr;
  height: 51px;
  line-height: 51px;
  border-radius: 0px;
  border: 1px solid rgba(151, 151, 151, 0.1);
  padding: 0 30px;
  box-sizing: border-box;
  margin-bottom: 2px;
}

.tr:hover {
  background-color: rgba(39, 171, 255, 0.1);
}

.td {
  font-weight: 400;
  font-size: 16px;
  display: flex;
  align-items: center;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loading-text {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #A9C5C8;
}

/* 滚动条样式 */
.table-body::-webkit-scrollbar {
  width: 6px;
}

.table-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb {
  background: rgba(39, 171, 255, 0.5);
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: rgba(39, 171, 255, 0.8);
}

// 资源池样式
.resource-pool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 25px;
  height: 100%;
  padding: 30px 12px;
  box-sizing: border-box;
  margin: 10px;
}

.resource-pool-item {
  background: linear-gradient(230deg, rgba(27, 44, 53, 0.22) 0%, #053552 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(243deg, rgba(88, 208, 253, 0.4), rgba(94, 215, 254, 0.55)) 1 1;
  padding: 0;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 180px;

  .resource-title-bg {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 40px;
    background: url('@assets/images/img_title_bg.png') no-repeat center center / 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;

    .resource-title {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
    }
  }

  .resource-stats {
    padding: 35px 15px 5px;
    text-align: center;
    
    .resource-value {
      font-family: DIN Alternate;
      font-size: 20px;
      color: #ffffff;
      text-shadow: 0px 0px 10px rgba(37, 161, 255, 0.5);
    }

    .resource-unit {
      font-family: DIN Alternate;
      font-weight: normal;
      font-size: 12px;
      color: #B6E4FF;
      margin-left: 4px;
    }
  }

  .resource-chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 10px 15px;

    .chart-bg {
      width: 170px;
      height: 140px;
      background: url('@assets/images/wide.png') no-repeat center/cover;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .circle-progress {
        width: 100px;
        height: 100px;
        background: transparent;
      }
    }

    .chart-label {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      line-height: 1.2;
      white-space: nowrap;
      position: absolute;
      bottom: 33px;
    }
  }
}

.env-monitoring {
  display: flex;
  gap: 15px;
  min-height: 280px; // 增加容器高度以显示完整温度计
  /* 让左右两部分合理分配空间 */
  & > .env-cards {
    flex: 1;
  }
}
.left-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}
.right-chart {
  flex: 0 0 auto; // 固定宽高，避免变形
  width: 160px; // 适当调整宽度
  height: 280px; // 增加高度以显示完整温度计
}
</style>

