# 部署指南

## 常见错误：UncaughtSyntaxError: Unexpected token '<'

这个错误通常发生在浏览器尝试加载JavaScript文件时却收到了HTML内容。

### 原因分析

1. **静态资源404**: 服务器找不到JS/CSS文件，返回了404页面（HTML格式）
2. **路径配置错误**: `base`路径配置与实际部署路径不匹配
3. **服务器配置问题**: SPA应用需要特殊的服务器配置

### 解决方案

#### 1. 检查构建配置

根据部署环境调整 `vite.config.js` 中的 `base` 配置：

```javascript
// 根目录部署（如 https://domain.com/）
base: '/'

// 子目录部署（如 https://domain.com/app/）
base: '/app/'

// 相对路径部署（推荐，适用于大多数情况）
base: './'
```

#### 2. 服务器配置

**Nginx**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

**Apache**:
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # 处理静态资源
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^.*$ - [NC,L]
    
    # SPA路由支持
    RewriteRule ^.*$ index.html [NC,L]
</IfModule>
```

#### 3. 构建和部署步骤

```bash
# 1. 安装依赖
npm install

# 2. 构建项目
npm run build

# 3. 检查构建结果
ls -la dist/

# 4. 部署到服务器
# 将 dist/ 目录的内容上传到服务器的网站根目录
```

#### 4. 本地测试构建结果

```bash
# 使用serve工具本地预览构建结果
npx serve dist

# 或使用http-server
npx http-server dist
```

### 调试技巧

1. **检查Network面板**: 查看哪些资源加载失败
2. **检查Console错误**: 查看具体的错误信息
3. **检查Response内容**: 确认是否返回了HTML而不是JS/CSS
4. **清理缓存**: 强制刷新浏览器缓存（Ctrl+F5）

### 不同部署平台配置

#### Vercel
```json
// vercel.json
{
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

#### Netlify
```toml
# netlify.toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### GitHub Pages
需要在 `public/` 目录添加 `.nojekyll` 文件，并配置：
```javascript
// vite.config.js
base: '/repository-name/'
```

### 检查清单

- [ ] `vite.config.js` 中的 `base` 配置正确
- [ ] 服务器配置了SPA路由支持
- [ ] 静态资源路径正确
- [ ] 清理了浏览器缓存
- [ ] 检查了Network面板的错误
- [ ] 路由配置包含404处理 