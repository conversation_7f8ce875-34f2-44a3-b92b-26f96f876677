<template>
    <div class="left-content">
    <div class="container" style="width: 315px; justify-content: space-evenly;border: 1px solid;border: none;">
      <div class="sidebar-content">
        <!-- 搜索框 -->
        <div class="search-box">
          <div class="search-input-wrapper">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="7" cy="7" r="6" stroke="#7BCCFF" stroke-width="1.5"/>
              <path d="m13 13 4.35 4.35" stroke="#7BCCFF" stroke-width="1.5"/>
            </svg>
            <input 
              type="text" 
              class="search-input" 
              placeholder="搜索机柜名称"
              v-model="searchKeyword"
            />
          </div>
        </div>

        <!-- 机柜区域 -->
        <div class="cabinet-section">
          <div class="device-title">机柜管理</div>
          <div class="section-content">
            <!-- 机房列表 -->
            <div class="room-list">
              <div 
                class="room-item" 
                v-for="room in roomList" 
                :key="room.id"
              >
                <div class="room-header" @click="toggleItem(room)">
                  <div class="room-icon">
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                      <rect x="2" y="3" width="10" height="8" rx="1" stroke="#27ABFF" stroke-width="1.5" fill="none"/>
                      <rect x="4" y="1" width="6" height="2" rx="0.5" stroke="#27ABFF" stroke-width="1.5" fill="none"/>
                    </svg>
                  </div>
                  <span class="room-name">{{ room.name }}</span>
                  <div class="room-arrow" :class="{ 'expanded': room.expanded }">
                    <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                      <path d="M2 3L4 5L6 3" stroke="#7BCCFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
                
                <div class="cabinet-list" v-show="room.expanded">
                  <div 
                    class="cabinet-item" 
                    v-for="cabinet in getFilteredCabinets(room.child)" 
                    :key="cabinet.id"
                  >
                    <div class="cabinet-header" 
                         @click.stop="selectCabinet(cabinet)"
                         :class="{ 'selected': selectedCabinet?.id === cabinet.id }">
                      <div class="cabinet-icon">
                         <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                          <rect x="1" y="2" width="10" height="8" rx="1" stroke="#7BCCFF" stroke-width="1.5" fill="none"/>
                        </svg>
                      </div>
                      <span class="cabinet-name">{{ cabinet.name }}</span>
                      <span class="cabinet-count">({{ cabinet.child?.length || 0 }})</span>
                      <div class="cabinet-arrow" 
                           :class="{ 'expanded': cabinet.expanded }"
                           @click.stop="toggleItem(cabinet)">
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                          <path d="M2 3L4 5L6 3" stroke="#7BCCFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                    </div>
                    
                    <div class="device-list" v-show="cabinet.expanded">
                      <div 
                        class="device-item" 
                        v-for="device in cabinet.child" 
                        :key="device.id"
                        @click="selectDevice(device)"
                        :class="{ 'selected': selectedDevice?.id === device.id }"
                      >
                        <div class="device-icon">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <rect x="1" y="2" width="10" height="8" rx="1" stroke="#7BCCFF" stroke-width="1.5" fill="none"/>
                            <circle cx="3" cy="4" r="0.5" fill="#27ABFF"/>
                            <circle cx="3" cy="6" r="0.5" fill="#27ABFF"/>
                            <circle cx="3" cy="8" r="0.5" fill="#27ABFF"/>
                          </svg>
                        </div>
                        <span class="device-name">{{ device.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备区域 -->
        <div class="equipment-section">
          <div class="device-title">设备监控</div>
          <div class="section-content">
            <!-- 设备列表 -->
            <div class="equipment-list">
              <div 
                class="equipment-category" 
                v-for="category in equipmentCategories" 
                :key="category.id"
              >
                <div class="equipment-header" @click="toggleEquipmentCategory(category)">
                  <div class="equipment-icon">
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                      <rect x="2" y="3" width="10" height="8" rx="1" stroke="#27ABFF" stroke-width="1.5" fill="none"/>
                      <circle cx="6" cy="7" r="1" fill="#27ABFF"/>
                    </svg>
                  </div>
                  <span class="equipment-name">{{ category.name }}</span>
                  <span class="equipment-count">({{ category.count }})</span>
                  <div class="equipment-arrow" :class="{ 'expanded': category.expanded }">
                    <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                      <path d="M2 3L4 5L6 3" stroke="#7BCCFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
                
                <div class="equipment-device-list" v-show="category.expanded">
                  <div 
                    class="equipment-device-item" 
                    v-for="device in category.devices" 
                    :key="device.id"
                    @click="selectEquipmentDevice(device)"
                    :class="{ 'selected': selectedEquipmentDevice?.id === device.id }"
                  >
                    <div class="equipment-device-icon">
                      <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                        <circle cx="6" cy="6" r="2" stroke="#7BCCFF" stroke-width="1.5" fill="none"/>
                        <circle cx="6" cy="6" r="0.5" fill="#27ABFF"/>
                      </svg>
                    </div>
                    <span class="equipment-device-name">{{ device.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="right-content">

    <div class="container right-container" style="width: 616px;" :class="{ 'sidebar-expanded': sidebarExpanded, 'cabinet-view': viewType === 'cabinet' }">
      <!-- 机柜视图 -->
      <template v-if="viewType === 'cabinet'">
        <div class="container-item">
          <div class="content-header">
            机柜信息
          </div>
          <div class="content-body jigui-box">
            <img class="jigui-img" src="@assets/images/jigui.png" alt="">
            <div class="jigui-content">
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">机柜名称：</span>
                <span class="jigui-content-item-value">{{ cabinetInfo.cabinetName || selectedCabinet?.name || '--' }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">机房位置：</span>
                <span class="jigui-content-item-value">{{ cabinetInfo.computerRoomNumber || '--' }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">机柜编号：</span>
                <span class="jigui-content-item-value">{{ cabinetInfo.cabinetNumber || '--' }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">机柜类型：</span>
                <span class="jigui-content-item-value">{{ cabinetInfo.cabinetType === '1' ? '标准机柜' : cabinetInfo.cabinetType || '--' }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备总数：</span>
                <span class="jigui-content-item-value">{{ deviceStats.total || 0 }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">正常设备：</span>
                <span class="jigui-content-item-value">{{ deviceStats.normal || 0 }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">告警设备：</span>
                <span class="jigui-content-item-value" :style="{ color: deviceStats.warning > 0 ? '#FF6B35' : '' }">{{ deviceStats.warning || 0 }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">离线设备：</span>
                <span class="jigui-content-item-value">{{ deviceStats.offline || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="container-item">
          <div class="content-header">
            <div class="header-left">
              <div class="device-icon">
              </div>
              <span>设备统计</span>
            </div>
          </div>
          <div class="content-body device-table-box">
            <div class="device-stats-wrapper">
              <div class="device-stats-bg">
                <div class="device-stats-main">
                  <div class="stat-main-item">
                    <span class="stat-main-label">设备总计</span>
                    <span class="stat-main-value">{{ deviceStats.total || 0 }}</span>
                  </div>
                </div>

                <div class="device-stats-details">
                  <div class="stat-detail-item">
                    <div class="stat-dot normal"></div>
                    <span class="stat-detail-label">正常设备</span>
                    <span class="stat-detail-value">{{ deviceStats.normal || 0 }}</span>
                  </div>
                  <div class="stat-detail-item">
                    <div class="stat-dot warning"></div>
                    <span class="stat-detail-label">告警设备</span>
                    <span class="stat-detail-value">{{ deviceStats.warning || 0 }}</span>
                  </div>
                  <div class="stat-detail-item">
                    <div class="stat-dot offline"></div>
                    <span class="stat-detail-label">离线设备</span>
                    <span class="stat-detail-value">{{ deviceStats.offline || 0 }}</span>
                  </div>
                  <div class="stat-detail-item">
                    <div class="stat-dot unregistered"></div>
                    <span class="stat-detail-label">未注册设备</span>
                    <span class="stat-detail-value">{{ deviceStats.unregistered || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="device-table">
              <div class="table-header">
                <div class="table-cell serial">序号</div>
                <div class="table-cell device-name">设备名称/ESN</div>
                <div class="table-cell device-type">设备类型</div>
                <div class="table-cell ip-address">IP地址</div>
                <div class="table-cell status">状态</div>
              </div>
              <div class="table-body">
                <div 
                  class="table-row" 
                  v-for="(device, index) in deviceTableData" 
                  :key="device.id"
                  @click="selectDevice(device)"
                >
                  <div class="table-cell serial">{{ index + 1 }}</div>
                  <div class="table-cell device-name">{{ device.name }}</div>
                  <div class="table-cell device-type">{{ device.type }}</div>
                  <div class="table-cell ip-address">{{ device.ip }}</div>
                  <div class="table-cell status">
                    <span class="status-dot" :class="device.statusClass"></span>
                    <span>{{ device.status }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 设备视图 -->
      <template v-else-if="viewType === 'device'">
        <div class="container-item">
          <div class="content-header">
            设备信息
          </div>
          <div class="content-body jigui-box">
            <img class="jigui-img" src="@assets/images/jigui.png" alt="">
            <!-- 物理主机信息 -->
            <div v-if="isPhysicalHost" class="jigui-content">
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备名称：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.name }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">IP地址：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.ip }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">硬件类型：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.hardwareType }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">系统版本：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.systemVersion }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">运行的云主机：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.cloudHostCount }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">状态：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.status }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">CPU：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.cpuUsage }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">内存：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.memoryUsage }}</span>
              </div>
            </div>
            <!-- 交换机信息 -->
            <div v-else-if="isSwitch" class="jigui-content">
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备名称：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.name }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">IP地址：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.ip }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备类型：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.deviceType }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备厂商：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.vendor }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">软件版本：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.softwareVersion }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">MAC地址：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.macAddress }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">设备位置：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.location }}</span>
              </div>
              <div class="jigui-content-item">
                <div class="jigui-circle"></div>
                <span class="jigui-content-item-title">状态：</span>
                <span class="jigui-content-item-value">{{ deviceInfo.status }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="container-item">
          <div class="content-header">
            设备分析
          </div>
        <div class="content-body shebeifenxi-box">
          <template v-if="isPhysicalHost">
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei4.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  网卡
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.networkCard }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei1.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  硬盘使用率
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.diskUsage }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei2.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  集群类型
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.clusterType }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei2.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  存储使用率
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.storageUsage }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei6.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  CPU剩余
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.cpuRemaining }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei3.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  内存容量
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.memoryCapacity }}
                </div>
              </div>
            </div>
            <div class="shebeifenxi-item">
              <div class="shebeifenxi-item-img">
                <img src="@assets/images/shebei8.png" alt="">
              </div>
              <div class="shebeifenxi-item-box">
                <div class="shebeifenxi-item-title">
                  开机时长
                </div>
                <div class="shebeifenxi-item-value">
                  {{ deviceAnalysis.uptime }}
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="isSwitch">
            <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei4.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    管理IP
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.managementIp }}
                  </div>
                </div>
              </div>
              <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei1.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    序列号(ESN)
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.esn }}
                  </div>
                </div>
              </div>
              <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei2.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    维保单位
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.maintenanceUnit }}
                  </div>
                </div>
              </div>
              <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei2.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    投入使用时间
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.commissioningDate }}
                  </div>
                </div>
              </div>
              <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei3.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    系统版本
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.osVersion }}
                  </div>
                </div>
              </div>
              <div class="shebeifenxi-item">
                <div class="shebeifenxi-item-img">
                  <img src="@assets/images/shebei8.png" alt="">
                </div>
                <div class="shebeifenxi-item-box">
                  <div class="shebeifenxi-item-title">
                    启动时间
                  </div>
                  <div class="shebeifenxi-item-value">
                    {{ deviceAnalysis.uptime }}
                  </div>
                </div>
              </div>
          </template>
        </div>
      </div>
      <div class="container-item">
        <div class="content-header analysis-header">
          <div 
            :class="['analysis-title', { 'active': analysisType === 'cpu' }]"
            @click="setAnalysisType('cpu')"
          >
            CPU分析
          </div>
          <div 
            :class="['analysis-title', { 'active': analysisType === 'memory' }]"
            @click="setAnalysisType('memory')"
          >
            内存分析
          </div>
        </div>
        <div v-if="analysisType === 'cpu'">
          <div class="content-body cpu-box">
            <div class="cpu-info">
              <img class="cpu-img" src="@assets/images/cpu.png" alt="">
              <span>CPU</span>
              <img class="cpu-jt" src="@assets/images/cpu-jt.png" alt="">
              <span>{{ deviceInfo.cpuUsage }}</span>
              <img class="cpu-jt" src="@assets/images/cpu-jt.png" alt="">
              <span>{{ physicalHostInfo?.cpu?.coreCount ? `${physicalHostInfo.cpu.coreCount} 核` : '--' }}</span>
            </div>
            <div class="cpu-line">
              <LineChart :data="cpuData" type="2" unit="%" />
            </div>
          </div>
        </div>
        <div v-if="analysisType === 'memory'">
            <div class="content-body cpu-box">
              <div class="cpu-info">
                <img class="cpu-img" src="@assets/images/shebei3.png" alt="">
                <span>内存</span>
                <img class="cpu-jt" src="@assets/images/cpu-jt.png" alt="">
                <span>{{ deviceInfo.memoryUsage }}</span>
                <img class="cpu-jt" src="@assets/images/cpu-jt.png" alt="">
                <span>{{ physicalHostInfo?.memoryMb ? `${(physicalHostInfo.memoryMb / 1024).toFixed(0)} GB` : '--' }}</span>
              </div>
              <div class="cpu-line">
                <LineChart :data="memoryData" type="2" unit="%" />
              </div>
            </div>
        </div>
      </div>
      </template>
    </div>
  </div>
  
  
  </template>
  
  <script setup>
  import { useRouter } from 'vue-router'
  import { ref, onMounted, computed } from 'vue'
  import LineChart from '@components/LineChart.vue'
  
  const router = useRouter()
  
  const defaultCpuData = [
    { time: '2:00', temperature: 15 }, { time: '4:00', temperature: 25 },
    { time: '6:00', temperature: 24 }, { time: '8:00', temperature: 12 },
    { time: '10:00', temperature: 28 }, { time: '12:00', temperature: 30 },
    { time: '14:00', temperature: 24 }, { time: '16:00', temperature: 24 },
    { time: '18:00', temperature: 24 }, { time: '20:00', temperature: 24 },
    { time: '22:00', temperature: 24 }, { time: '23:00', temperature: 24 },
  ]

  const defaultMemoryData = [
    { time: '2:00', temperature: 45 }, { time: '4:00', temperature: 55 },
    { time: '6:00', temperature: 50 }, { time: '8:00', temperature: 60 },
    { time: '10:00', temperature: 58 }, { time: '12:00', temperature: 62 },
    { time: '14:00', temperature: 57 }, { time: '16:00', temperature: 59 },
    { time: '18:00', temperature: 65 }, { time: '20:00', temperature: 63 },
    { time: '22:00', temperature: 68 }, { time: '23:00', temperature: 70 },
  ]
  
  const searchKeyword = ref('')
  const selectedDevice = ref(null)
  const selectedCabinet = ref(null)
  const viewType = ref('cabinet') // 'cabinet' 或 'device'
  const sidebarExpanded = ref(false)
  const physicalHostInfo = ref(null)
  const netDeviceInfo = ref(null)
  const analysisType = ref('cpu') // 'cpu' or 'memory'
  const cpuData = ref([])
  const memoryData = ref([])
  
  // 机房列表数据
  const roomList = ref([])

  // 机柜信息数据
  const cabinetInfo = ref({
    cabinetName: '',
    computerRoomNumber: '',
    cabinetNumber: '',
    cabinetType: ''
  })

  // 设备统计数据
  const deviceStats = ref({
    total: 0,
    normal: 0,
    warning: 0,
    offline: 0,
    unregistered: 0
  })

  // 设备表格数据
  const deviceTableData = ref([])

  // 设备分类数据
  const equipmentCategories = ref([
    {
      id: 'cameras',
      name: '摄像头',
      count: 6,
      expanded: true,
      devices: [
        { id: 'camera1', name: '摄像头1' },
        { id: 'camera2', name: '摄像头2' },
        { id: 'camera3', name: '摄像头3' },
        { id: 'camera4', name: '摄像头4' },
        { id: 'camera5', name: '摄像头5' },
        { id: 'camera6', name: '摄像头6' }
      ]
    },
    {
      id: 'interRowAC',
      name: '列间空调',
      count: 3,
      expanded: true,
      devices: [
        { id: 'interRowAC1', name: '列间空调1' },
        { id: 'interRowAC2', name: '列间空调2' },
        { id: 'interRowAC3', name: '列间空调3' }
      ]
    },
    {
      id: 'ups',
      name: 'UPS',
      count: 2,
      expanded: true,
      devices: [
        { id: 'ups1', name: 'UPS1' },
        { id: 'ups2', name: 'UPS2' }
      ]
    },
    {
      id: 'powerCabinet',
      name: '配电柜',
      count: 3,
      expanded: true,
      devices: [
        { id: 'powerCabinet1', name: '配电柜1' },
        { id: 'powerCabinet2', name: '配电柜2' },
        { id: 'powerCabinet3', name: '配电柜3' }
      ]
    },
    {
      id: 'roomAC',
      name: '机房空调',
      count: 2,
      expanded: true,
      devices: [
        { id: 'roomAC1', name: '机房空调1' },
        { id: 'roomAC2', name: '机房空调2' }
      ]
    },
    {
      id: 'accessControl',
      name: '门禁',
      count: 2,
      expanded: true,
      devices: [
        { id: 'accessControl1', name: '门禁1' },
        { id: 'accessControl2', name: '门禁2' }
      ]
    }
  ])

  // 选中的设备
  const selectedEquipmentDevice = ref(null)

  const isPhysicalHost = computed(() => !!physicalHostInfo.value);
  const isSwitch = computed(() => !!netDeviceInfo.value);

  // 设备信息计算属性
  const deviceInfo = computed(() => {
    if (isPhysicalHost.value) {
      const data = physicalHostInfo.value
      return {
        name: data.name || selectedDevice.value?.name || '--',
        ip: data.ip || '--',
        hardwareType: data.cpu?.type || '--',
        systemVersion: data.osName || '--',
        cloudHostCount: data.cloudServiceCount || '0',
        status: data.status === 'running' ? '正常' : '异常',
        cpuUsage: data.cpuRatio ? `${(data.cpuRatio * 100).toFixed(0)}%` : '--',
        memoryUsage: data.memoryRatio ? `${(data.memoryRatio * 100).toFixed(0)}%` : '--',
      }
    }
    if (isSwitch.value) {
      const data = netDeviceInfo.value
      return {
          name: data.aliasname || selectedDevice.value?.name || '--',
          ip: data.neip || '--',
          deviceType: data.netype || '--',
          vendor: data.nevendorname || '--',
          softwareVersion: data.version || '--',
          macAddress: data.nemac || '--',
          location: data.neposition || '--',
          status: selectedDevice.value?.status || '--',
      }
    }
    return { // 默认空状态
      name: selectedDevice.value?.name || '--',
      ip: '--',
      hardwareType: '--',
      systemVersion: '--',
      cloudHostCount: '--',
      status: '--',
      cpuUsage: '--',
      memoryUsage: '--',
      deviceType: '--',
      vendor: '--',
      softwareVersion: '--',
      macAddress: '--',
      location: '--',
    }
  })

  // 设备分析计算属性
  const deviceAnalysis = computed(() => {
    if (isPhysicalHost.value) {
      const data = physicalHostInfo.value
      let uptime = '--'
      if (data.uptime) {
        let seconds = data.uptime;
        const days = Math.floor(seconds / (3600*24));
        seconds  -= days*3600*24;
        const hrs   = Math.floor(seconds / 3600);
        seconds  -= hrs*3600;
        const mnts = Math.floor(seconds / 60);
        seconds  -= mnts*60;
        
        if (days > 0) {
          uptime = `${days}天${hrs}小时`;
        } else if (hrs > 0) {
          uptime = `${hrs}小时${mnts}分钟`;
        } else {
          uptime = `${mnts}分钟`;
        }
      }

      return {
        networkCard: data.ip || '--',
        diskUsage: data.storageRatio ? `${(data.storageRatio * 100).toFixed(0)}%` : '--',
        clusterType: data.clusterName || '--',
        storageUsage: data.storageMb ? `${(data.storageMb / 1024 / 1024).toFixed(2)} TB` : '--',
        cpuRemaining: data.cpuRatio ? `${(100 - data.cpuRatio * 100).toFixed(0)}%` : '--',
        memoryCapacity: data.memoryMb ? `${(data.memoryMb / 1024).toFixed(0)} GB` : '--',
        uptime: uptime
      }
    }
    if (isSwitch.value) {
        const data = netDeviceInfo.value;
        let uptime = '--'
        if (data.neruntime) {
            const date = new Date(parseInt(data.neruntime));
            if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                uptime = `${year}-${month}-${day} ${hours}:${minutes}`;
            }
        }
        return {
            managementIp: data.neip || '--',
            esn: data.neesn || '--',
            maintenanceUnit: data.maintainunit || '--',
            commissioningDate: data.putintoactiontime || '--',
            osVersion: data.neosversion || '--',
            uptime: uptime
        }
    }
    return {
      networkCard: '--',
      diskUsage: '--',
      clusterType: '--',
      storageUsage: '--',
      cpuRemaining: '--',
      memoryCapacity: '--',
      uptime: '--'
    }
  })

  const getDeviceTypeName = (type) => {
    switch (String(type)) {
      case '2': return '交换机';
      case '3': return '物理主机';
      default: return '未知类型';
    }
  }

  const getTreeData = async () => {
    try {
      // 使用fetch调用接口
      const response = await fetch('http://*************:8099/api/zjRotatingRing/jftree?computerRoomNumber=A')
      const result = await response.json()
      const data = result.code === 0 ? result.data : null
      
              if (data) {
          // 递归为每个节点添加 expanded 属性
          const addExpanded = (nodes) => {
            return nodes.map(node => {
              const newNode = { ...node, expanded: true }
              if (node.child && node.child.length > 0) {
                newNode.child = addExpanded(node.child)
              }
              return newNode
            })
          }
          
          roomList.value = addExpanded(data)
        console.log('机房树形数据加载成功:', roomList.value)
        
        // 默认选择第一个机柜
        if (roomList.value.length > 0 && roomList.value[0].child && roomList.value[0].child.length > 0) {
          selectCabinet(roomList.value[0].child[0])
        }
              } else {
          console.error('获取机房树形数据失败: 数据为空')
        }
    } catch (error) {
      console.error('获取机房树形数据失败:', error)
      
      // 如果代理请求失败，尝试直接请求（用于生产环境）
      try {
        const response = await fetch('http://*************:8099/api/zjRotatingRing/jftree?computerRoomNumber=A')
        const result = await response.json()
        
        if (result.code === 0 && result.data) {
          // 递归为每个节点添加 expanded 属性
          const addExpanded = (nodes) => {
            return nodes.map(node => {
              const newNode = { ...node, expanded: true }
              if (node.child && node.child.length > 0) {
                newNode.child = addExpanded(node.child)
              }
              return newNode
            })
          }
          
          roomList.value = addExpanded(result.data)
          console.log('机房树形数据加载成功（直接请求）:', roomList.value)
          
          // 默认选择第一个机柜
          if (roomList.value.length > 0 && roomList.value[0].child && roomList.value[0].child.length > 0) {
            selectCabinet(roomList.value[0].child[0])
          }
        } else {
          console.error('获取机房树形数据失败:', result.msg)
        }
      } catch (directError) {
        console.error('直接请求也失败:', directError)
      }
    }
  }

  onMounted(() => {
    getTreeData()
  })
  
  // 切换机房或机柜展开状态
  const toggleItem = (item) => {
    item.expanded = !item.expanded
  }
  
  // 选择机柜
  const selectCabinet = async (cabinet) => {
    selectedCabinet.value = cabinet
    selectedDevice.value = null
    viewType.value = 'cabinet'
    console.log('选择机柜:', cabinet)
    
    // 调用接口获取机柜相关数据
    if (cabinet.id) {
      await Promise.all([
        getCabinetInfo(cabinet.id),
        getDeviceStats(cabinet.id),
        getDeviceList(cabinet.id)
      ])
    }
  }
  
  // 获取机柜信息
  const getCabinetInfo = async (cabinetId) => {
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/getJfCabinetInfo?id=${cabinetId}`)
      const result = await response.json()
      const data = result.code === 0 ? result.data : null
      console.log('机柜信息原始数据:', data)
      
      if (data) {
        cabinetInfo.value = {
          cabinetName: data.cabinetName || '',
          computerRoomNumber: data.computerRoomNumber || '',
          cabinetNumber: data.cabinetNumber || '',
          cabinetType: data.cabinetType || ''
        }
        console.log('机柜信息获取成功:', cabinetInfo.value)
      } else {
        console.error('获取机柜信息失败 - 数据为空:', data)
      }
    } catch (error) {
      console.error('获取机柜信息失败:', error)
      // 这里可以添加其他错误处理逻辑
    }
  }

  // 获取设备统计信息
  const getDeviceStats = async (cabinetId) => {
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/jgquipmentStatistics?cabinetId=${cabinetId}`)
      const result = await response.json()
      const data = result.code === 0 ? result.data : null
      console.log('设备统计原始数据:', data)
      
      if (data) {
        deviceStats.value = {
          total: parseInt(data.sbzs) || 0,        // 设备总数
          normal: parseInt(data.zcsbs) || 0,      // 正常设备
          warning: parseInt(data.gjsbs) || 0,     // 告警设备
          offline: parseInt(data.lxsbs) || 0,     // 离线设备
          unregistered: parseInt(data.wzcsbs) || 0 // 未注册设备
        }
        console.log('设备统计信息获取成功:', deviceStats.value)
      } else {
        console.error('获取设备统计信息失败 - 数据为空:', data)
      }
    } catch (error) {
      console.error('获取设备统计信息失败:', error)
      // 这里可以添加其他错误处理逻辑
    }
  }

  // 获取设备列表
  const getDeviceList = async (cabinetId) => {
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/jgquipmentList?cabinetId=${cabinetId}`)
      const result = await response.json()
      const data = result.code === 0 ? result.data : null
      console.log('设备列表原始数据:', data)
      
      if (data && Array.isArray(data)) {
        deviceTableData.value = data.map((device, index) => {
          // 根据设备状态确定状态类和显示文本
          let status = '正常'
          let statusClass = 'normal'
          
          if (device.alarmStatus === '1') {
            status = '告警'
            statusClass = 'warning'
          } else if (device.deviceOnline === '1') {
            status = '离线'
            statusClass = 'offline'
          } else if (!device.deviceId) {
            status = '未注册'
            statusClass = 'unregistered'
          }
          
          return {
            id: device.deviceId || `device_${index}`,
            name: device.deviceName || '未知设备',
            type: getDeviceTypeName(device.deviceType),
            ip: device.ip || '--', // 如果接口没有IP字段，暂时显示--
            status: status,
            statusClass: statusClass,
            originalData: device // 保存原始数据以备后用
          }
        })
        console.log('设备列表获取成功:', deviceTableData.value)
      } else {
        console.error('获取设备列表失败 - 数据格式错误:', data)
        console.error('data is Array:', Array.isArray(data))
        deviceTableData.value = []
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      // 这里可以添加其他错误处理逻辑
      deviceTableData.value = []
    }
  }
  
  // 获取物理主机信息
  const getPhysicalHostInfo = async (deviceId) => {
    if (!deviceId || !deviceId.startsWith('host-')) {
      console.log('非物理主机，不获取详细信息:', deviceId)
      physicalHostInfo.value = null
      return
    }
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/getPhysicalHostInfo?id=${deviceId}`)
      const result = await response.json()
      if (result.code === 0 && result.data) {
        physicalHostInfo.value = result.data
        console.log('物理主机信息获取成功:', physicalHostInfo.value)
      } else {
        console.error('获取物理主机信息失败:', result.msg)
        physicalHostInfo.value = null
      }
    } catch (error) {
      console.error('获取物理主机信息接口调用失败:', error)
      physicalHostInfo.value = null
    }
  }
  
  // 获取性能数据
  const getPerformanceData = async (serviceId) => {
    if (!serviceId) {
      cpuData.value = defaultCpuData
      memoryData.value = defaultMemoryData
      return
    }
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/getPhysicalHostZx?serviceId=${serviceId}`)
      const result = await response.json()
      if (result.code === 0 && result.data) {
        if (result.data.cpu && result.data.cpu.length > 0) {
            cpuData.value = result.data.cpu.map(item => ({
              time: `${item.time}:00`,
              temperature: item.value * 100
            }))
        } else {
            cpuData.value = defaultCpuData
        }

        if (result.data.memory && result.data.memory.length > 0) {
            memoryData.value = result.data.memory.map(item => ({
              time: `${item.time}:00`,
              temperature: item.value * 100
            }))
        } else {
            memoryData.value = defaultMemoryData
        }
        
        console.log('Performance data fetched or defaults used')
      } else {
        console.error('Failed to get performance data or data is empty:', result.msg)
        cpuData.value = defaultCpuData
        memoryData.value = defaultMemoryData
      }
    } catch (error) {
      console.error('Error fetching performance data:', error)
      cpuData.value = defaultCpuData
      memoryData.value = defaultMemoryData
    }
  }

  // 获取网关设备信息
  const getNetDeviceInfo = async (deviceId) => {
    if (!deviceId) {
      console.log('No deviceId, skipping net device info fetch')
      netDeviceInfo.value = null
      return
    }
    try {
      const response = await fetch(`http://*************:8099/api/zjRotatingRing/getNetDevcieInfo?id=${deviceId}`)
      const result = await response.json()
      if (result.code === 0 && result.data) {
        netDeviceInfo.value = result.data
        console.log('网关设备信息获取成功:', netDeviceInfo.value)
      } else {
        console.error('获取网关设备信息失败:', result.msg)
        netDeviceInfo.value = null
      }
    } catch (error) {
      console.error('获取网关设备信息接口调用失败:', error)
      netDeviceInfo.value = null
    }
  }

  // 选择设备
  const selectDevice = (device) => {
    selectedDevice.value = device
    selectedCabinet.value = null
    viewType.value = 'device'
    console.log('选择设备:', device)
    
    // 重置数据
    physicalHostInfo.value = null
    netDeviceInfo.value = null
    cpuData.value = []
    memoryData.value = []

    const deviceType = device.originalData ? device.originalData.deviceType : device.type;

    // 如果是物理主机(类型为'3')，则获取详细信息
    if (String(deviceType) === '3') {
      getPhysicalHostInfo(device.id)
      getPerformanceData(device.id)
    } else if (String(deviceType) === '2') { // 如果是交换机(类型为'2')
      getNetDeviceInfo(device.id)
      cpuData.value = defaultCpuData
      memoryData.value = defaultMemoryData
    } else {
      physicalHostInfo.value = null // 清空旧数据
      netDeviceInfo.value = null
      cpuData.value = defaultCpuData
      memoryData.value = defaultMemoryData
    }

    // 发送设备选择事件给ThreeScene
    // 直接使用设备ID，因为deviceId就是设备的构件名
    console.log('🔧 设备ID处理:', {
      设备ID: device.id,
      设备名称: device.name,
      选中机柜: selectedCabinet.value?.name
    });
    
    const deviceSelectEvent = new CustomEvent('deviceSelected', {
      detail: {
        deviceId: device.id,
        deviceName: device.name,
        originalDeviceId: device.id,
        cabinetName: selectedCabinet.value?.name
      }
    })
    window.dispatchEvent(deviceSelectEvent)
  }
  
  // 切换分析视图
  const setAnalysisType = (type) => {
    analysisType.value = type
  }

  // 切换设备分类展开状态
  const toggleEquipmentCategory = (category) => {
    category.expanded = !category.expanded
  }

  // 选择设备
  const selectEquipmentDevice = (device) => {
    selectedEquipmentDevice.value = device
    console.log('选择设备:', device)
    
    // 这里可以添加设备选择后的处理逻辑
    // 例如：显示设备详情、发送事件给3D场景等
    const deviceSelectEvent = new CustomEvent('equipmentDeviceSelected', {
      detail: {
        deviceId: device.id,
        deviceName: device.name,
        deviceType: getDeviceTypeByCategory(device.id)
      }
    })
    window.dispatchEvent(deviceSelectEvent)
  }

  // 根据设备ID获取设备类型
  const getDeviceTypeByCategory = (deviceId) => {
    if (deviceId.includes('camera')) return '摄像头'
    if (deviceId.includes('interRowAC')) return '列间空调'
    if (deviceId.includes('ups')) return 'UPS'
    if (deviceId.includes('powerCabinet')) return '配电柜'
    if (deviceId.includes('roomAC')) return '机房空调'
    if (deviceId.includes('accessControl')) return '门禁'
    return '未知设备'
  }

  // 过滤机柜列表
  const getFilteredCabinets = (cabinets) => {
    if (!searchKeyword.value || !cabinets) {
      return cabinets
    }
    return cabinets.filter(cabinet => 
      cabinet.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  </script>
  
  <style scoped lang="scss">
  .left-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 315px;
  height: 100%;
  background: url('@assets/images/left-bg2.png') no-repeat center center / 100% 100%;
  pointer-events: all;

  .container {
    margin-left: 17px;
  }
}

.right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 616px;
  height: 100%;
  background: url('@assets/images/right-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;
  display: flex;

  .container {
    margin-right: 17px;
    margin-left: auto;
  }
}

// 设备侧边栏样式
.device-sidebar {
  position: absolute;
  top: 0;
  left: -37px;
  width: 40px;
  height: 80%;
  margin-top: 10%;
  transition: width 0.3s ease;
  z-index: 10;

  &.expanded {
    width: 238px;
    left: -280px;
  }
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background: url('@assets/images/toleft.png') no-repeat center center / 100% 100%;
  width: 38px;
  height: 90px;
  // // background: linear-gradient(135deg, rgba(39, 171, 255, 0.2) 0%, rgba(65, 239, 255, 0.1) 100%);
  // // border: 1px solid rgba(39, 171, 255, 0.3);
  // // border-radius: 8px 0px 0px 8px;
  // // display: flex;
  // // align-items: center;
  // // justify-content: center;
  // // cursor: pointer;
  // // backdrop-filter: blur(10px);
  // // transition: all 0.3s ease;

  // background: linear-gradient( 90deg, rgba(14,38,67,0) 0%, rgba(45,111,183,0.92) 100%);
  // border-radius: 8px 8px 8px 8px;
  // border: 1px solid;
  // border-image: linear-gradient(271deg, rgba(83, 193, 255, 0.28), rgba(83, 193, 255, 0), rgba(56, 183, 255, 0)) 1 1;

  &.expanded {
    left: -37px;
  }

}

.toggle-icon {
  transition: transform 0.3s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

.sidebar-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 90%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 搜索框样式
.search-box {
  margin-bottom: 20px;
  flex-shrink: 0;
}

// 区域布局样式
.cabinet-section, .equipment-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  &:first-of-type {
    margin-bottom: 16px;
  }
}

// 设备标题样式
.device-title {
  position: relative;
  font-weight: 500;
  font-size: 14px;
  margin: 10px 0 20px 0;

  &::before {
    position: absolute;
    top: 8px;
    left: -14px;
    content: '';
    display: block;
    width: 6px;
    height: 4px;
    background: #27ABFF;
  }
}

// 区域内容样式
.section-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  
  // 优化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(39, 171, 255, 0.6) 0%, rgba(39, 171, 255, 0.8) 100%);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, rgba(39, 171, 255, 0.8) 0%, rgba(39, 171, 255, 1) 100%);
      box-shadow: 0 0 6px rgba(39, 171, 255, 0.4);
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  height: 40px;
  background: rgba(20, 53, 78, 0.8);
  border: 1px solid rgba(123, 204, 255, 0.3);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;

  &:focus-within {
    border-color: rgba(39, 171, 255, 0.6);
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.2);
  }
}

.search-icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 14px;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }
}

// 机房列表样式
.room-list {
  .room-item {
    margin-bottom: 8px;

    .room-header {
      display: flex;
      align-items: center;
      padding: 12px 8px;
      background: rgba(20, 53, 78, 0.6);
      border: 1px solid rgba(39, 171, 255, 0.2);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(39, 171, 255, 0.1);
        border-color: rgba(39, 171, 255, 0.4);
      }
    }

    .room-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .room-name {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .room-arrow {
      transition: transform 0.3s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}

// 设备列表样式
.device-list {
  margin-top: 4px;
  padding-left: 8px;

  .device-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 2px;
    background: rgba(14, 38, 67, 0.4);
    border: 1px solid rgba(123, 204, 255, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.3);
    }

    &.selected {
      background: rgba(39, 171, 255, 0.2);
      border-color: rgba(39, 171, 255, 0.5);
    }

    .device-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .device-name {
      flex: 1;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 设备统计样式
.device-stats-wrapper {
  margin: 0 0 16px 0;
  padding: 0;
  flex-shrink: 0;
}

.device-stats-bg {
  width: 100%;
  height: 66px;
  background: 
    url('@assets/images/deviceTotal.png') no-repeat;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  position: relative;
  border-radius: 4px;
  margin-top: 10px;
  padding-left: 80px;
}

.device-stats-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.device-stats-main {
  margin-right: 20px;
  flex-shrink: 0;
  
  .stat-main-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    
    .stat-main-label {
      font-size: 13px;
      color: #B6E4FF;
      white-space: nowrap;
    }
    
    .stat-main-value {
      font-family: 'DIN Alternate', sans-serif;
      font-weight: bold;
      font-size: 20px;
      color: #27ABFF;
      text-shadow: 0px 0px 6px rgba(39, 171, 255, 0.5);
      min-width: 20px;
    }
  }
}

.device-stats-details {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
  
  .stat-detail-item {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
    
    .stat-dot {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      flex-shrink: 0;
      
      &.normal {
        background: #4CAF50 !important;
      }
      
      &.warning {
        background: #FF6B35 !important;
      }
      
      &.offline {
        background: #7BCCFF !important;
      }
      
      &.unregistered {
        background: #9E9E9E !important;
      }
    }
    
    .stat-detail-label {
      font-size: 11px;
      color: #B6E4FF;
      white-space: nowrap;
      margin-right: 3px;
    }
    
    .stat-detail-value {
      font-family: 'DIN Alternate', sans-serif;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
      min-width: 12px;
    }
  }
}

// 设备表格样式
.device-table-box {
  margin: 0 8px 23px 8px;
  padding: 0;
  background: linear-gradient(230deg, rgba(27,44,53,0.22) 0%, #053552 100%);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid;
  border-image: linear-gradient(243deg, rgba(0, 128, 204, 0.5), rgba(0, 128, 204, 0.75)) 1 1;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(90deg, rgba(6, 25, 55, 0.8) 0%, rgba(14, 38, 67, 0.9) 100%);
  border-bottom: 1px solid rgba(39, 171, 255, 0.3);
  font-size: 14px;
  font-weight: 500;
  color: #B6E4FF;
}

.table-body {
  flex: 1;
  overflow-y: auto;
  
  // 滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(39, 171, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(39, 171, 255, 0.5);
    }
  }
}

.table-row {
  display: flex;
  align-items: center;
  height: 42px;
  border-bottom: 1px solid rgba(39, 171, 255, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(6, 25, 55, 0.2);

  &:nth-child(even) {
    background: rgba(14, 38, 67, 0.2);
  }

  &:hover {
    background: rgba(39, 171, 255, 0.15) !important;
  }

  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  padding: 8px 12px;
  font-size: 13px;
  color: #ffffff;
  display: flex;
  align-items: center;

  &.serial {
    width: 60px;
    justify-content: center;
    color: #B6E4FF;
  }

  &.device-name {
    flex: 1;
    min-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.device-type {
    width: 80px;
    justify-content: center;
  }

  &.ip-address {
    width: 120px;
    justify-content: center;
    font-family: 'DIN Alternate', monospace;
  }

  &.status {
    width: 80px;
    justify-content: center;
    gap: 6px;
  }
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  
  &.normal {
    background: #4CAF50;
  }
  
  &.warning {
    background: #FF6B35;
  }
  
  &.offline {
    background: #7BCCFF;
  }
  
  &.unregistered {
    background: #7BCCFF;
  }
}

// 机柜头部选中状态样式
.cabinet-header.selected {
  background: rgba(39, 171, 255, 0.2) !important;
  border-color: rgba(39, 171, 255, 0.5) !important;
}

// 右侧容器调整
.right-container {
  transition: margin-left 0.3s ease;

  // &.sidebar-expanded {
  //   left: -225px
  // }
}



.container {
  width: 456px;
  height: 968px;
  background: linear-gradient(271deg, rgba(6, 25, 55, 0.3) 0%, rgba(9, 61, 102, 0.4) 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  // 毛玻璃效果
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  // 机柜视图的特殊布局
  &.cabinet-view {
    justify-content: flex-start;
    
    .container-item {
      &:first-child {
        flex: 0.8; // 机柜信息占1/3
      }
      
      &:nth-child(2) {
        flex: 2.2; // 机柜设备占2/3
      }
    }
  }
}

.content-header {
  width: 100%;
  min-height: 54px;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  padding: 12px 34px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .device-icon {
      display: flex;
      align-items: center;
    }
    
    span {
      font-size: 22px;
      color: #ffffff;
    }
  }

  // 设备统计样式 - 使用新的背景图和布局
  .device-stats-wrapper {
    margin: 0 8px 16px 8px;
    padding: 0;
    flex-shrink: 0;
  }

  .device-stats-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    
    svg {
      width: 20px;
      height: 20px;
    }
  }

  .device-stats-main {
    margin-right: 20px;
    flex-shrink: 0;
    
    .stat-main-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      
      .stat-main-label {
        font-size: 13px;
        color: #B6E4FF;
        white-space: nowrap;
      }
      
      .stat-main-value {
        font-family: 'DIN Alternate', sans-serif;
        font-weight: bold;
        font-size: 20px;
        color: #27ABFF;
        text-shadow: 0px 0px 6px rgba(39, 171, 255, 0.5);
        min-width: 20px;
      }
    }
  }

  .device-stats-details {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-detail-item {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-shrink: 0;
      
      .stat-dot {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        flex-shrink: 0;
        
        &.normal {
          background: #4CAF50;
        }
        
        &.warning {
          background: #FF6B35;
        }
        
        &.offline {
          background: #7BCCFF;
        }
        
        &.unregistered {
          background: #9E9E9E;
        }
      }
      
      .stat-detail-label {
        font-size: 11px;
        color: #B6E4FF;
        white-space: nowrap;
        margin-right: 3px;
      }
      
      .stat-detail-value {
        font-family: 'DIN Alternate', sans-serif;
        font-weight: bold;
        font-size: 14px;
        color: #ffffff;
        min-width: 12px;
      }
    }
  }

  .arrow-right-box {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .arrow-right {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}
.left-item {
  width: 211px;
  height: 102px;
  margin-left: 63px;
  background: linear-gradient( 90deg, rgba(14,38,67,0) 0%, rgba(45,111,183,0.62) 100%);
  border-radius: 8px 8px 8px 8px;
  // border: 1px solid;
  border-image: linear-gradient(271deg, rgba(83, 193, 255, 0.28), rgba(83, 193, 255, 0), rgba(56, 183, 255, 0)) 1 1;
  display: flex;
  align-items: center;

  .left-item-title {
    font-weight: 400;
    font-size: 16px;
    color: rgba(255,255,255,0.8);
    margin-left: 12px;
  }

  .left-item-content {
    font-family: DIN Alternate;
    font-weight: bold;
    font-size: 24px;
    letter-spacing: 1px;
    margin-left: 12px;

    span {
      font-family: pingfang;
      font-weight: 400;
      font-size: 14px;
      color: #97B0C8;
    }
  }
}

.jigui-box {
  display: flex;
  // width: 600px;
  // height: 108px;
  background: linear-gradient( 230deg, rgba(27,44,53,0.22) 0%, #053552 100%);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid;
  border-image: linear-gradient(243deg, rgba(0, 128, 204, 0.5), rgba(0, 128, 204, 0.75)) 1 1;
  margin: 21px 8px 23px 8px;
  padding: 16px;
  box-sizing: border-box;

  .jigui-img {
    height: 148px;
  }

  .jigui-content {
    display: flex;
    flex: 1;
    margin-left: 16px;
    flex-wrap: wrap;

    .jigui-content-item {
      width: 50%;
      display: flex;
      align-items: center;

      .jigui-circle {
        width: 6px;
        height: 6px;
        background: #25A1FF;
        margin-right: 11px;
        border-radius: 50%;
      }

      .jigui-content-item-title {
        width: 98px;
        font-weight: 400;
        font-size: 14px;
        color: #B6E4FF;
        flex-shrink: 0;
      }

      .jigui-content-item-value {
        font-family: DIN Alternate, DIN Alternate;
        font-weight: bold;
        font-size: 18px;
        text-shadow: 0px 0px 10px rgba(37,161,255,0.5);
        /* word-break: break-all; */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
      }

      
    }
  }
}

.shebeifenxi-box {
  margin: 30px 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;

  .shebeifenxi-item {
    width: 184px;
    height: 54px;
    display: flex;
    align-items: center;
    background: url('@assets/images/shebeifenxi-bg.png') no-repeat center center / 100% 100%;

    .shebeifenxi-item-img {
      width: 54px;
      height: 54px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .shebeifenxi-item-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      padding: 0 8px 0 10px;
      box-sizing: border-box;
      overflow: hidden;

      .shebeifenxi-item-title {
        font-weight: 400;
        font-size: 14px;
        color: #B6E4FF;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-right: 5px;
      }

      .shebeifenxi-item-value {
        font-family: DIN Alternate, DIN Alternate;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        text-shadow: 0px 0px 10px rgba(37,161,255,0.5);
        /* word-wrap: break-word; */
        text-align: right;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    
  }
}

.cpu-box {
  

  .cpu-info {
    margin: 18px 42px;
    background: url('@assets/images/cpu-bg.png') no-repeat center center / 100% 100%;
    display: flex;
    align-items: center;
    height: 76px;

    .cpu-img {
      width: 54px;
      height: 59px;
      margin-left: 32px;
      margin-right: 40px;
    }

    .cpu-jt {
      margin: 0 30px;
    }

    span {}
  }

  .cpu-line {
    width: 100%;
    height: 235px;
    padding: 20px;
    box-sizing: border-box;
  }
}

.cabinet-list {
  margin-top: 4px;
  padding-left: 16px;

  .cabinet-item {
    margin-bottom: 4px;
  }

  .cabinet-header {
    display: flex;
    align-items: center;
    padding: 10px 8px;
    background: rgba(20, 53, 78, 0.4);
    border: 1px solid rgba(39, 171, 255, 0.15);
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.3);
    }
  }

  .cabinet-icon {
    margin-right: 8px;
  }

  .cabinet-name {
    flex: 1;
    font-size: 13px;
    color: #E0F2FF;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cabinet-count {
    font-size: 12px;
    color: #7BCCFF;
    margin-right: 8px;
  }

  .cabinet-arrow {
    transition: transform 0.3s ease;
    &.expanded {
      transform: rotate(180deg);
    }
  }
}

// 设备列表样式
.device-list {
  margin-top: 4px;
  padding-left: 8px;

  .device-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 2px;
    background: rgba(14, 38, 67, 0.4);
    border: 1px solid rgba(123, 204, 255, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.3);
    }

    &.selected {
      background: rgba(39, 171, 255, 0.2);
      border-color: rgba(39, 171, 255, 0.5);
    }

    .device-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .device-name {
      flex: 1;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.analysis-header {
  justify-content: flex-start;
  gap: 24px;
}

.analysis-title {
  cursor: pointer;
  color: #B6E4FF;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.analysis-title.active {
  color: #FFF;
  opacity: 1;
  font-weight: bold;
}

// 设备分类列表样式
.equipment-list {
  // 移除顶部间距，因为现在有区域标题
}

.equipment-category {
  margin-bottom: 8px;

  .equipment-header {
    display: flex;
    align-items: center;
    padding: 12px 8px;
    background: rgba(20, 53, 78, 0.6);
    border: 1px solid rgba(39, 171, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.4);
    }
  }

  .equipment-icon {
    margin-right: 8px;
    flex-shrink: 0;
  }

  .equipment-name {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .equipment-count {
    font-size: 12px;
    color: #7BCCFF;
    margin-right: 8px;
  }

  .equipment-arrow {
    transition: transform 0.3s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

// 设备子项列表样式
.equipment-device-list {
  margin-top: 4px;
  padding-left: 16px;

  .equipment-device-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 2px;
    background: rgba(14, 38, 67, 0.4);
    border: 1px solid rgba(123, 204, 255, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.3);
    }

    &.selected {
      background: rgba(39, 171, 255, 0.2);
      border-color: rgba(39, 171, 255, 0.5);
    }

    .equipment-device-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .equipment-device-name {
      flex: 1;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style> 