<template>
  <div class="pie-chart-container">
    <div ref="chartRef" class="pie-chart"></div>
    <div class="chart-center">
      <div class="center-number">{{ totalCount }}</div>
      <div class="center-label">设备</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'

// Props定义
const props = defineProps({
  data: {
    type: Array,
    default: () => [
      { name: '正常设备', value: 3218, color: '#218CDF' },
      { name: '主要设备', value: 2056, color: '#09D8DB' },
      { name: '警告设备', value: 1043, color: '#FCB24A' },
      { name: '紧急设备', value: 1022, color: '#FF744C' }
    ]
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '180px'
  }
})

const chartRef = ref(null)
let chart = null

// 计算总数
const totalCount = computed(() => {
  return props.data.reduce((sum, item) => sum + item.value, 0)
})

// 生成扇形的曲面参数方程
const getParametricEquation = (startRatio, endRatio, isSelected, isHovered, k, h) => {
  // 计算
  const midRatio = (startRatio + endRatio) / 2
  const startRadian = startRatio * Math.PI * 2
  const endRadian = endRatio * Math.PI * 2
  const midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x(u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },
    y(u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },
    z(u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1
      }
      // 当前图形的高度是Z根据h（每个value的值决定的）
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
const getPie3D = (pieData, internalDiameterRatio) => {
  const series = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  const legendData = []
  const k = typeof internalDiameterRatio !== 'undefined' 
    ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) 
    : 1 / 3

  // 计算最大值，用于高度比例计算
  const maxValue = Math.max(...pieData.map(item => item.value))
  const minHeight = 8  // 最小高度
  const maxHeight = 25 // 最大高度

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i += 1) {
    sumValue += pieData[i].value

    const seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k,
      },
    }

    if (typeof pieData[i].itemStyle !== 'undefined') {
      const { itemStyle } = pieData[i]
      typeof pieData[i].itemStyle.color !== 'undefined' 
        ? (itemStyle.color = pieData[i].itemStyle.color) 
        : null
      // 设置完全不透明
      itemStyle.opacity = 1.0
      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数
  for (let i = 0; i < series.length; i += 1) {
    endValue = startValue + series[i].pieData.value

    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    
    // 根据数值大小计算高度，数值越大高度越高
    const heightRatio = series[i].pieData.value / maxValue
    const dynamicHeight = minHeight + (maxHeight - minHeight) * heightRatio
    
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      dynamicHeight // 使用动态计算的高度
    )

    startValue = endValue
    legendData.push(series[i].name)
  }

  // 准备待返回的配置项
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      show: false, // 禁用tooltip提示
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 10,
      top: '-16%',
      viewControl: {
        alpha: 36,
        rotateSensitivity: 1,
        zoomSensitivity: 0,
        panSensitivity: 0,
        autoRotate: false, // 关闭自动旋转
        autoRotateSpeed: 0,
        distance: 136,
      },
      postEffect: {
        enable: false,
        bloom: {
          enable: true,
          bloomIntensity: 0.1,
        },
        SSAO: {
          enable: true,
          quality: 'medium',
          radius: 2,
        },
      },
    },
    series,
  }
  return option
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  // 转换数据格式
  const pieData = props.data.map(item => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: item.color,
      opacity: 1.0 // 设置完全不透明
    }
  }))
  
  const option = getPie3D(pieData, 0.59)
  chart.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chart) return
  
  const pieData = props.data.map(item => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: item.color,
      opacity: 1.0 // 设置完全不透明
    }
  }))
  
  const option = getPie3D(pieData, 0.59)
  chart.setOption(option)
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听容器尺寸变化
const resizeChart = () => {
  if (chart) {
    chart.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法给父组件
defineExpose({
  updateChart,
  resizeChart
})
</script>

<style scoped lang="scss">
.pie-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('@assets/images/pie-3d-bg.png') no-repeat center center / 80%;
}

.pie-chart {
  width: 100%;
  height: 100%;
}

.chart-center {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #FFFFFF;
  pointer-events: none;
  z-index: 10;

  .center-number {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 24px;
    color: #FFFFFF;
    margin-bottom: 2px;
  }

  .center-label {
    font-weight: 400;
    font-size: 16px;
    color: #97B0C8;
  }
}
</style> 