// 设置系统缩放比，适配各分辨率
export const refreshScale = () => {
  let baseWidth = document.documentElement.clientWidth;
  let baseHeight = document.documentElement.clientHeight;
  let appStyle = document.getElementById("app").style;
  let scaleRate = baseHeight / 1080;
  appStyle.transformOrigin = "left top";
  appStyle.transform = `scale(${scaleRate}) translateX(-50%)`;
  appStyle.width = `${baseWidth / scaleRate}px`;
};
