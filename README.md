# 机房大屏系统

基于 Vite + Vue3 + Element Plus + Vue Router 的项目框架

## 技术栈

- **构建工具**: Vite
- **前端框架**: Vue 3 (Composition API)
- **UI 组件库**: Element Plus
- **路由管理**: Vue Router 4
- **图标**: Element Plus Icons

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── router/          # 路由配置
│   └── index.js     # 路由主配置文件
├── views/           # 页面组件
│   ├── Home.vue     # 首页
│   └── About.vue    # 关于页面
├── App.vue          # 根组件
├── main.js          # 应用入口
└── style.css        # 全局样式
```

## 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 功能特性

- ✅ Vue 3 Composition API
- ✅ Element Plus 组件库集成
- ✅ Vue Router 路由管理
- ✅ 响应式布局
- ✅ 现代化 UI 设计
- ✅ 开发热重载
