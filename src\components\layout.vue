<template>
  <div class="layout">
    <div class="header">
      <div class="time">
        <span class="time-text">{{ currentTime }}</span>
        <span class="week">{{ currentWeek }}</span>
      </div>
      <div class="header-title">数字孪生机房运维管理</div>
      <div class="weather">
        <span>{{ weatherData.temperature }}</span>
        <span>{{ weatherData.weather }}</span>
        <span>{{ weatherData.windDirection }}</span>
        <span>{{ weatherData.city }}</span>
      </div>
      <div class="nav">
        <div class="nav-item-group">
          <span class="nav-item" :class="{ 'nav-item-active': isActiveRoute('overview') }"
            @click="navigateTo('overview')">机房总览</span>
          <span class="nav-item" :class="{ 'nav-item-active': isActiveRoute('alarm') }"
            @click="navigateTo('alarm')">告警态势</span>
        </div>
        <div class="nav-item-group">
          <span class="nav-item" :class="{ 'nav-item-active': isActiveRoute('device') }"
            @click="navigateTo('device')">设备管理</span>
          <span class="nav-item" :class="{ 'nav-item-active': isActiveRoute('network') }"
            @click="navigateTo('network')">网络拓扑图</span>
        </div>
      </div>
    </div>
    <div class="content">
      <router-view />
    </div>
    <teleport to="#app" v-if="showThreeScene">
      <ThreeScene />
    </teleport>

    <div class="three-tools" v-if="showThreeTools" :style="{ left: threeToolsLeft + 'px' }">
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '机房信息' }"
        @click="handleThreeToolsClick('机房信息')">
        <img class="three-tools-item-img" src="@assets/images/tool1.png" alt="" srcset="">
        <span>机房信息</span>
      </div>
      <!-- <div class="three-tools-item" :class="{ 'active': threeToolsActive === '机房温度' }"
        @click="handleThreeToolsClick('机房温度')">
        <img class="three-tools-item-img" src="@assets/images/tool2.png" alt="" srcset="">
        <span>机房温度</span>
      </div>
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '存储空间' }"
        @click="handleThreeToolsClick('存储空间')">
        <img class="three-tools-item-img" src="@assets/images/tool3.png" alt="" srcset="">
        <span>存储空间</span>
      </div>
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '机房热图' }"
        @click="handleThreeToolsClick('机房热图')">
        <img class="three-tools-item-img" src="@assets/images/tool4.png" alt="" srcset="">
        <span>机房热图</span>
      </div>
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '漫游巡检' }"
        @click="handleThreeToolsClick('漫游巡检')">
        <img class="three-tools-item-img" src="@assets/images/tool5.png" alt="" srcset="">
        <span>漫游巡检</span>
      </div> -->
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '机房实景' }"
        @click="handleThreeToolsClick('机房实景')">
        <img class="three-tools-item-img" src="@assets/images/tool5.png" alt="" srcset="">
        <span>机房实景</span>
      </div>
      <div class="three-tools-item" :class="{ 'active': threeToolsActive === '复位' }"
        @click="handleThreeToolsClick('复位')">
        <img class="three-tools-item-img" src="@assets/images/tool6.png" alt="" srcset="">
        <span>复位</span>
      </div>
    </div>
  </div>

</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { getWeatherData } from '@api/weather'
import ThreeScene from '@components/ThreeScene.vue'

// 设置中文本地化
dayjs.locale('zh-cn')

const route = useRoute()
const router = useRouter()

const threeToolsActive = ref('机房信息')

// 当前激活的路由
const currentRoute = ref(route.name)

// 控制ThreeScene和three-tools的显示状态
const showThreeScene = ref(true)
const showThreeTools = ref(true)
const threeToolsLeft = ref(492)

// 时间相关
const currentTime = ref('')
const currentWeek = ref('')
let timeInterval = null

// 天气相关
const weatherData = ref({
  temperature: '--℃',
  weather: '--',
  windDirection: '--',
  city: '南京市'
})



// 更新时间的函数
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('YYYY-MM-DD HH:mm:ss')
  currentWeek.value = now.format('dddd')
}

// 获取天气数据的函数
const fetchWeatherData = async () => {
  try {
    const data = await getWeatherData()
    weatherData.value = data
  } catch (error) {
    console.error('获取天气数据失败:', error)
    // 使用默认数据
    weatherData.value = {
      temperature: '17℃',
      weather: '晴',
      windDirection: '东北风',
      city: '南京'
    }
  }
}

// 监听路由变化
watch(() => route.name, (newRoute) => {
  currentRoute.value = newRoute

  // 根据路由调整界面显示状态
  if (newRoute === 'device') {
    // 设备管理页面：显示ThreeScene和three-tools，调整three-tools位置
    showThreeScene.value = true
    showThreeTools.value = true
    threeToolsLeft.value = 360
  } else if (newRoute === 'network') {
    // 网络拓扑图页面：隐藏ThreeScene和three-tools
    showThreeScene.value = false
    showThreeTools.value = false
  } else {
    // 其他页面：显示ThreeScene和three-tools，使用默认位置
    showThreeScene.value = true
    showThreeTools.value = true
    threeToolsLeft.value = 492
  }
}, { immediate: true })

// 组件挂载时开始定时器
onMounted(() => {
  updateTime() // 立即更新一次时间
  timeInterval = setInterval(updateTime, 1000) // 每秒更新时间

  fetchWeatherData() // 获取一次天气
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// 判断当前路由是否激活
const isActiveRoute = (routeName) => {
  return currentRoute.value === routeName
}

// 导航到指定路由
const navigateTo = (routeName) => {
  if (currentRoute.value !== routeName) {
    router.push({ name: routeName })
  }
}

const handleThreeToolsClick = (type) => {
  threeToolsActive.value = type;
  if (type === '机房信息') {
    window.clearScene();
    window.showAllDeviceLabels();
  } else if (type === '机房温度') {
    window.clearScene();
    window.addHighTemperatureEffect();
  } else if (type === '存储空间') {
    window.clearScene();
    window.addStorageSpaceEffect();
  } else if (type === '机房热图') {
    window.clearScene();
    window.addHeatmap();
  } else if (type === '漫游巡检') {
    window.clearScene();
    window.hideAllDeviceLabels();
    window.addManyou();
  } else if (type === '复位') {
    window.clearScene();
    window.showAllDeviceLabels();
    window.resetToInitialView();
  }
}


</script>

<style scoped lang="scss">
.layout {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: url('@assets/images/bg1.png') no-repeat center top / 100% 159px,
    url('@assets/images/bg2.png') no-repeat center bottom / 100% 99px;
  pointer-events: none;

  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 96px;
    z-index: 100;
    background: url('@assets/images/header.png') no-repeat center center / 100% 100%;
    pointer-events: all;

    .header-title {
      position: absolute;
      top: 9px;
      left: 50%;
      transform: translateX(-50%);
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 42px;
      letter-spacing: 5px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #ffffff 0%, #EEEEEE 50%, #A8DCFF 100%);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
    }

    .time {
      position: absolute;
      top: 25px;
      left: 42px;
      display: flex;
      align-items: center;

      .time-text {
        font-family: DIN Alternate;
        font-size: 20px;
      }

      .week {
        font-weight: 600;
        font-size: 12px;
        color: #A4C9FD;
        margin-left: 12px;
      }
    }

    .weather {
      position: absolute;
      top: 25px;
      right: 42px;
      display: flex;
      align-items: center;
      font-family: DIN Alternate, pingfang;
      font-size: 16px;
      color: #A4C9FD;

      span {
        margin-left: 10px;
      }

    }

    .nav {
      position: absolute;
      width: 65%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      top: 18px;
      left: 50%;
      transform: translateX(-50%);

      .nav-item-group {
        width: 22%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .nav-item {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 28px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(180deg, #ffffff 0%, #EEEEEE 50%, #A8DCFF 100%);
          color: transparent;
          background-clip: text;
          -webkit-background-clip: text;
          opacity: 0.6;
          cursor: pointer;
        }

        .nav-item-active {
          opacity: 1;
        }
      }
    }
  }
}

.content {
  position: relative;
  width: 100%;
  height: calc(100% - 90px);
  z-index: 1;
  margin-top: 90px;
  overflow: hidden;
}

.three-tools {
  position: absolute;
  top: 99px;
  pointer-events: all;
  z-index: 0;
  transition: left 0.3s ease;

  .three-tools-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    text-shadow: 2px 2px 4px rgba(16, 56, 109, 0.5);
    text-align: center;
    margin-bottom: 26px;
    opacity: 0.5;
    cursor: pointer;

    span {
      margin-top: -10px;
    }
  }

  .active {
    opacity: 1;
  }

}
</style>