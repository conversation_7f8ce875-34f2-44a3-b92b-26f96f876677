/**
 * 天气API服务
 * 使用高德地图天气API
 */

import axios from 'axios'

// 高德地图天气API 配置
const AMAP_CONFIG = {
  baseURL: 'https://restapi.amap.com/v3/weather',
  apiKey: '9d89a767b2349b60ea3b5c77e3eab5fc',
  city: '320100' // 南京的城市编码
}

// 风向映射
const windDirectionMap = {
  '东': '东风',
  '南': '南风',
  '西': '西风',
  '北': '北风',
  '东北': '东北风',
  '东南': '东南风',
  '西南': '西南风',
  '西北': '西北风',
  '无风向': '无风'
}

// 处理风向信息
const formatWindDirection = (windDirection, windPower) => {
  const direction = windDirectionMap[windDirection] || windDirection
  return windPower ? `${direction}${windPower}级` : direction
}

// 使用高德地图API获取天气
const getWeatherFromAmap = async () => {
  try {
    const response = await axios.get(
      `${AMAP_CONFIG.baseURL}/weatherInfo`, {
        params: {
          key: AMAP_CONFIG.apiKey,
          city: AMAP_CONFIG.city,
          extensions: 'base' // base:返回实况天气 all:返回预报天气
        }
      }
    )
    
    console.log('高德天气API响应:', response.data)
    
    if (response.data.status !== '1') {
      throw new Error(`高德API错误: ${response.data.info}`)
    }
    
    const weatherInfo = response.data.lives[0]
    
    return {
      temperature: `${weatherInfo.temperature}℃`,
      weather: weatherInfo.weather,
      windDirection: formatWindDirection(weatherInfo.winddirection, weatherInfo.windpower),
      city: weatherInfo.city || '南京',
      humidity: `${weatherInfo.humidity}%`,
      updateTime: weatherInfo.reporttime || new Date().toLocaleTimeString()
    }
  } catch (error) {
    console.error('高德地图天气API 调用失败:', error)
    throw error
  }
}

// 主要的天气获取函数
export const getWeatherData = async () => {
  try {
    // 使用高德地图API获取真实天气数据
    return await getWeatherFromAmap()
    
  } catch (error) {
    console.error('获取天气数据失败', error)
  }
}

export default {
  getWeatherData,
} 