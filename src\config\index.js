/**
 * 项目配置文件
 */

// 应用配置
export const APP_CONFIG = {
  title: import.meta.env.VITE_APP_TITLE || '机房大屏系统',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  description: '基于 Vue3 + Element Plus 的机房大屏系统'
}

// API 配置
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  retryCount: 3
}

// 开发配置
export const DEV_CONFIG = {
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  mode: import.meta.env.MODE
}

// 主题配置
export const THEME_CONFIG = {
  primaryColor: '#409eff',
  successColor: '#67c23a',
  warningColor: '#e6a23c',
  dangerColor: '#f56c6c',
  infoColor: '#909399'
}

// 路由配置
export const ROUTER_CONFIG = {
  defaultRoute: '/',
  loginRoute: '/login',
  homeRoute: '/'
}

// 存储配置
export const STORAGE_CONFIG = {
  tokenKey: 'token',
  userInfoKey: 'userInfo',
  themeKey: 'theme',
  langKey: 'language'
}

// 分页配置
export const PAGINATION_CONFIG = {
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper'
}

// 上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptTypes: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'],
  action: '/api/upload'
}

// 导出所有配置
export default {
  APP_CONFIG,
  API_CONFIG,
  DEV_CONFIG,
  THEME_CONFIG,
  ROUTER_CONFIG,
  STORAGE_CONFIG,
  PAGINATION_CONFIG,
  UPLOAD_CONFIG
} 