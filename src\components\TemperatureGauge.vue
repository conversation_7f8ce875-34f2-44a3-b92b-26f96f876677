<template>
  <div class="temperature-gauge" ref="gaugeContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  temperature: {
    type: [Number, String],
    default: 0
  },
  maxTemp: {
    type: Number,
    default: 35 // 最高安全温度
  },
  minTemp: {
    type: Number,
    default: 18 // 最低安全温度
  }
})

const gaugeContainer = ref(null)
let chartInstance = null

// 计算温度健康评分 (0-100)
const calculateHealthScore = (temp) => {
  if (typeof temp === 'string' && temp === '--') return 0
  
  const numTemp = Number(temp)
  if (isNaN(numTemp)) return 0
  
  const optimalTemp = 22 // 最佳温度
  const tempRange = props.maxTemp - props.minTemp
  
  if (numTemp >= props.minTemp && numTemp <= props.maxTemp) {
    // 在安全范围内，根据与最佳温度的偏差计算评分
    const deviation = Math.abs(numTemp - optimalTemp)
    const maxDeviation = Math.max(optimalTemp - props.minTemp, props.maxTemp - optimalTemp)
    return Math.max(60, 100 - (deviation / maxDeviation) * 40)
  } else if (numTemp < props.minTemp) {
    // 温度过低
    const belowRange = props.minTemp - numTemp
    return Math.max(0, 60 - belowRange * 10)
  } else {
    // 温度过高
    const aboveRange = numTemp - props.maxTemp
    return Math.max(0, 60 - aboveRange * 15)
  }
}

const initChart = () => {
  if (!gaugeContainer.value) return
  
  chartInstance = echarts.init(gaugeContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  const healthScore = calculateHealthScore(props.temperature)
  const tempValue = typeof props.temperature === 'string' && props.temperature === '--' ? 0 : Number(props.temperature)
  
  const dataArr = [{
    value: healthScore,
    name: '温度健康评分'
  }]
  
  const color = new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
    offset: 0,
    color: '#5CF9FE'
  }, {
    offset: 0.17,
    color: '#468EFD'
  }, {
    offset: 0.9,
    color: '#468EFD'
  }, {
    offset: 1,
    color: '#5CF9FE'
  }])
  
  const colorSet = [
    [healthScore / 100, color],
    [1, '#15337C']
  ]
  
  const rich = {
    white: {
      fontSize: 14,
      color: '#fff',
      fontWeight: '500',
      padding: [-45, 0, 0, 0]
    },
    blue: {
      fontSize: 28,
      fontFamily: 'DIN Alternate',
      color: '#5CF9FE',
      fontWeight: '700',
      padding: [-35, 0, 0, 0],
    },
    temp: {
      fontSize: 16,
      color: '#B0E0FF',
      fontWeight: '400',
      padding: [10, 0, 0, 0]
    },
    radius: {
      width: 120,
      height: 24,
      borderWidth: 1,
      borderColor: '#0092F2',
      fontSize: 12,
      color: '#fff',
      backgroundColor: 'rgba(27, 33, 91, 0.8)',
      borderRadius: 8,
      textAlign: 'center',
    }
  }
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      formatter: "{a} <br/>{b} : {c}%"
    },
    series: [
      { // 内圆背景
        type: 'pie',
        radius: '85%',
        center: ['50%', '50%'],
        z: 0,
        itemStyle: {
          normal: {
            color: new echarts.graphic.RadialGradient(.5, .5, 1, [{
              offset: 0,
              color: 'rgba(17,24,43,0)'
            }, {
              offset: .5,
              color: 'rgba(28,42,91,.6)'
            }, {
              offset: 1,
              color: '#141C33',
            }], false),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          },
        },
        hoverAnimation: false,
        label: {
          show: false,
        },
        tooltip: {
          show: false
        },
        data: [100],
      },
      { // 外层辅助圈
        type: 'gauge',
        name: '外层辅助',
        radius: '74%',
        startAngle: '225',
        endAngle: '-45',
        splitNumber: '100',
        pointer: {
          show: false
        },
        detail: {
          show: false,
        },
        data: [{
          value: 1
        }],
        title: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: [
              [1, '#00FFFF']
            ],
            width: 1,
            opacity: 1
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        }
      },
      { // 主仪表盘
        type: 'gauge',
        radius: '70%',
        startAngle: '225',
        endAngle: '-45',
        pointer: {
          show: true,
          length: '60%',
          width: 3,
          itemStyle: {
            color: '#5CF9FE',
            shadowColor: '#5CF9FE',
            shadowBlur: 5
          }
        },
        detail: {
          formatter: function(value) {
            const num = Math.round(value)
            return `{temp|当前温度: ${tempValue}℃}`
          },
          rich: rich,
          offsetCenter: ['0%', '10%'],
        },
        data: dataArr,
        title: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colorSet,
            width: 12,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            opacity: 1
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
      },
      { // 灰色内圈
        name: '灰色内圈',
        type: 'gauge',
        z: 2,
        radius: '60%',
        startAngle: '225',
        endAngle: '-45',
        axisLine: {
          lineStyle: {
            color: [
              [1, '#018DFF']
            ],
            width: 1,
            opacity: 1,
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        pointer: {
          show: false
        },
        axisTick: {
          show: false
        },
        detail: {
          show: false
        }
      },
      { // 白色圈刻度
        name: "白色圈刻度",
        type: "gauge",
        radius: "60%",
        startAngle: 225,
        endAngle: -45,
        z: 4,
        axisTick: {
          show: false
        },
        splitLine: {
          length: 8,
          lineStyle: {
            width: 1,
            color: 'rgba(1,244,255, 0.6)'
          }
        },
        axisLabel: {
          color: 'rgba(255,255,255,0)',
          fontSize: 12,
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            opacity: 0
          }
        },
        detail: {
          show: false
        },
        data: [{
          value: 0,
          name: ""
        }]
      },
      { // 内圆
        type: 'pie',
        radius: '56%',
        center: ['50%', '50%'],
        z: 1,
        itemStyle: {
          normal: {
            color: new echarts.graphic.RadialGradient(.5, .5, .8, [{
              offset: 0,
              color: '#4978EC'
            }, {
              offset: .5,
              color: '#1E2B57'
            }, {
              offset: 1,
              color: '#141F3D'
            }], false),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          },
        },
        hoverAnimation: false,
        label: {
          show: false,
        },
        tooltip: {
          show: false
        },
        data: [100],
      },
    ]
  }
  
  chartInstance.setOption(option)
}

// 监听温度变化
watch(() => props.temperature, () => {
  nextTick(() => {
    updateChart()
  })
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>

<style scoped>
.temperature-gauge {
  width: 100%;
  height: 180px;
}
</style> 