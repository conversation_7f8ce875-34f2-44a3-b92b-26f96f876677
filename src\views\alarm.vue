<template>
  <div class="left-content">
    <div class="container">
      <!-- 今日告警 -->
      <div class="container-item today-alarm">
        <div class="content-header">今日告警</div>
        <div class="content-body today-alarm-info">
          <div class="alarm-item">
            <div class="hexagon-container">
              <img src="@/assets/images/gaojingxiufu.png" alt="告警修复" class="hexagon-img">
              <div class="alarm-label">今日告警修复</div>
              <div class="alarm-number">{{ todayAlarm.fixed }}</div>
            </div>
          </div>
          <div class="alarm-item">
            <div class="hexagon-container">
              <img src="@/assets/images/gaojingxinzeng.png" alt="告警新增" class="hexagon-img">
              <div class="alarm-label">今日告警新增</div>
              <div class="alarm-number">{{ todayAlarm.new }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备告警状态 -->
      <div class="container-item device-alarm-status">
        <div class="content-header">设备告警状态</div>
        <div class="content-body">
          <div class="status-stats">
            <div class="status-row">
              <div class="status-item blue">
                <span class="status-dot"></span>
                <span class="status-label">正常设备</span>
                <span class="status-count">{{ deviceStatus.normal }}</span>
              </div>
              <div class="status-item green">
                <span class="status-dot"></span>
                <span class="status-label">主要设备</span>
                <span class="status-count">{{ deviceStatus.main }}</span>
              </div>
            </div>
            <div class="status-row">
              <div class="status-item orange">
                <span class="status-dot"></span>
                <span class="status-label">警告设备</span>
                <span class="status-count">{{ deviceStatus.warning }}</span>
              </div>
              <div class="status-item red">
                <span class="status-dot"></span>
                <span class="status-label">紧急设备</span>
                <span class="status-count">{{ deviceStatus.urgent }}</span>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <Pie3DChart :data="pieChartData" />
          </div>
        </div>
      </div>

      <!-- 设备告警状况 -->
      <div class="container-item device-alarm-trend">
        <div class="content-header">设备告警状况</div>
        <div class="content-body">
          <LineChart :data="temperatureData" />
        </div>
      </div>
    </div>
  </div>

  <div class="right-content">
    <div class="container right-container">
      <!-- 最新告警设备 -->
      <div class="container-item latest-alarm">
        <div class="content-header">
          最新告警设备
        </div>
        <div class="content-body latest-alarm-info">
          <div class="donghuan-top">
            <div class="donghuan-range">
              <span class="range-dot red-dot"></span>
              <span class="range-text-title">紧急：</span><span class="range-text-value red">{{ urgentCount }}</span><span
                class="range-text-unit">次</span>
            </div>
            <div class="donghuan-range">
              <span class="range-dot orange-dot"></span>
              <span class="range-text-title">重要：</span><span class="range-text-value orange">{{ importantCount }}</span><span
                class="range-text-unit">次</span>
            </div>
            <div class="donghuan-range">
              <span class="range-dot yellow-dot"></span>
              <span class="range-text-title">次要：</span><span class="range-text-value yellow">{{ minorCount }}</span><span
                class="range-text-unit">次</span>
            </div>
            <div class="donghuan-range">
              <span class="range-dot blue-dot"></span>
              <span class="range-text-title">提示：</span><span class="range-text-value blue">{{ tipCount }}</span><span
                class="range-text-unit">次</span>
            </div>
          </div>
          <div class="alarm-list-header">
            <span class="alarm-list-header-item-title">序号</span>
            <span class="alarm-list-header-item-title">事件时间</span>
            <span class="alarm-list-header-item-title">事件内容</span>
            <span class="alarm-list-header-item-title">处理状态</span>
          </div>
          <div class="alarm-list">
            <div class="alarm-list-item" v-for="(item, index) in latestAlarms" :key="item.id">
              <span class="alarm-list-item-title">{{ index + 1 }}</span>
              <span class="alarm-list-item-title">{{ item.alarmTime }}</span>
              <span class="alarm-list-item-title">{{ item.alarmName }}</span>
              <span class="alarm-list-item-title" :class="getStatusClass(item.alarmStatus)">{{
                getStatusText(item.alarmStatus) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import Pie3DChart from '@/components/Pie3DChart.vue'
import LineChart from '@components/LineChart.vue'

const router = useRouter()

// 今日告警数据
const todayAlarm = ref({
  fixed: 0,
  new: 0
})

// 设备状态数据
const deviceStatus = ref({
  normal: 0,
  main: 0,
  warning: 0,
  urgent: 0
})

// 计算总设备数
const totalDevices = computed(() => {
  return deviceStatus.value.normal + deviceStatus.value.main +
    deviceStatus.value.warning + deviceStatus.value.urgent
})

// 饼图数据
const pieChartData = computed(() => [
  { name: '正常设备', value: deviceStatus.value.normal, color: '#00F5FF' },
  { name: '主要设备', value: deviceStatus.value.main, color: '#1E90FF' },
  { name: '警告设备', value: deviceStatus.value.warning, color: '#FFA500' },
  { name: '紧急设备', value: deviceStatus.value.urgent, color: '#FF6B6B' }
])

// 温度监测数据
const temperatureData = ref([])

// 最新告警列表
const latestAlarms = ref([])

// 告警级别统计数据 - 四个级别
const alarmLevelStats = ref({
  urgent: 0,    // 紧急
  important: 0, // 重要
  minor: 0,     // 次要
  tip: 0        // 提示
})

// 告警级别统计属性
const urgentCount = computed(() => alarmLevelStats.value.urgent)
const importantCount = computed(() => alarmLevelStats.value.important)
const minorCount = computed(() => alarmLevelStats.value.minor)
const tipCount = computed(() => alarmLevelStats.value.tip)

const getStatusText = (status) => {
  if (status === '0') return '待处理'
  if (status === '1') return '已完成'
  if (status === '2') return '处理中'
  return '未知'
}

const getStatusClass = (status) => {
  if (status === '0') return 'pending'
  if (status === '1') return 'completed'
  if (status === '2') return 'processing'
  return ''
}

const fetchTodayAlarmStats = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmtodayStatistics')
    const res = await response.json()
    if (res.code === 0 && res.data) {
      todayAlarm.value.fixed = res.data.qcsl
      todayAlarm.value.new = res.data.xzsl
    }
  } catch (error) {
    console.error('Failed to fetch today alarm stats:', error)
  }
}

const fetchDeviceStatus = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmtodayByTypeStatistics')
    const res = await response.json()
    if (res.code === 0 && Array.isArray(res.data)) {
      // 重置状态
      deviceStatus.value.normal = 0
      deviceStatus.value.main = 0
      deviceStatus.value.warning = 0
      deviceStatus.value.urgent = 0
      
      res.data.forEach(item => {
        switch (item.name) {
          case '1': // 紧急
            deviceStatus.value.urgent = item.value
            break
          case '2': // 重要
            deviceStatus.value.main = item.value
            break
          case '3': // 次要
            deviceStatus.value.warning = item.value
            break
          case '4': // 提示
            deviceStatus.value.normal = item.value
            break
        }
      })
    }
  } catch (error) {
    console.error('Failed to fetch device alarm status:', error)
  }
}

const fetchAlarmTrend = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmtodayZx')
    const res = await response.json()
    if (res.code === 0 && Array.isArray(res.data)) {
      temperatureData.value = res.data.map(item => ({
        time: `${item.time}:00`,
        temperature: item.value
      }))
    }
  } catch (error) {
    console.error('Failed to fetch alarm trend:', error)
  }
}

const fetchAlarmList = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmList')
    const res = await response.json()
    if (res.code === 0 && res.data) {
      latestAlarms.value = res.data
    }
  } catch (error) {
    console.error('Failed to fetch alarm list:', error)
  }
}

/**
 * 获取告警级别统计数据
 * @description 从API接口获取四个告警级别的统计数据
 */
const fetchAlarmLevelCount = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmLevelCount')
    const res = await response.json()
    console.log('告警级别统计数据获取:', res)
    
    if (res.code === 0 && res.data) {
      // 直接映射四个告警级别
      alarmLevelStats.value = {
        urgent: res.data['1'] || 0,    // 紧急
        important: res.data['2'] || 0, // 重要
        minor: res.data['3'] || 0,     // 次要
        tip: res.data['4'] || 0        // 提示
      }
    }
  } catch (error) {
    console.error('获取告警级别统计失败:', error)
    // 发生错误时保持默认值
    alarmLevelStats.value = {
      urgent: 0,
      important: 0,
      minor: 0,
      tip: 0
    }
  }
}

onMounted(() => {
  fetchTodayAlarmStats()
  fetchDeviceStatus()
  fetchAlarmTrend()
  fetchAlarmList()
  fetchAlarmLevelCount() // 添加告警级别统计接口调用
})

onUnmounted(() => {
})

</script>

<style scoped lang="scss">
.left-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 560px;
  height: 100%;
  background: url('@/assets/images/left-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;

  .container {
    margin-left: 17px;
  }
}

.right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 529px;
  height: 100%;
  background: url('@/assets/images/right-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;

  .container {
    margin-right: 17px;
    margin-left: auto;
  }
}

.container {
  width: 456px;
  height: 968px;
  background: linear-gradient(271deg, rgba(6, 25, 55, 0.3) 0%, rgba(9, 61, 102, 0.4) 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  // 毛玻璃效果
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0;
  overflow: hidden;
}

.container-item {
  flex: 1;
  display: flex;
  flex-direction: column;

  &.today-alarm {
    .content-body {
      height: 268px;
      overflow: hidden;
    }
  }

  &.device-alarm-status {
    .content-body {
      height: 328px;
      overflow: hidden;
      padding: 25px 40px;
      box-sizing: border-box;
    }
  }

  &.device-alarm-trend {
    .content-body {
      height: 210px;
      overflow: hidden;
      padding: 30px 20px;
      box-sizing: border-box;
    }
  }

  &.latest-alarm {
    height: 100%;
    overflow: hidden;

    .content-body {
      flex: 1;
      height: calc(100% - 54px);
    }
  }
}

.content-header {
  width: 100%;
  height: 54px;
  background: url('@/assets/images/title-bg.png') no-repeat center center / 100% 100%;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  line-height: 54px;
  padding-left: 34px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #FFFFFF;

  .arrow-right {
    width: 17px;
    height: 17px;
    cursor: pointer;
    margin-right: 20px;
  }
}

.content-body {
  flex: 1;
  overflow: hidden;
}

// 今日告警样式
.today-alarm-info {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: space-evenly;
}

.alarm-item {
  width: 178px;
  height: 214px;
  text-align: center;
  background: url('@assets/images/today-alarm-bg.png') no-repeat center center / 100% 100%;
}

.hexagon-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 60px 0;
  box-sizing: border-box;
}

.hexagon-img {
  width: 36px;
  height: 36px;
}

.alarm-number {
  font-family: DIN Alternate, DIN Alternate;
  font-weight: bold;
  font-size: 24px;
  color: #23E5FF;
}

.alarm-label {
  font-weight: 400;
  font-size: 16px;
  color: #97B0C8;
  line-height: 38px;
}

// 设备告警状态样式
.status-stats {
  margin-bottom: 20px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #C0DAEA;

  .status-dot {
    width: 8px;
    height: 8px;
    margin-right: 14px;
  }

  .status-label {
    margin-right: 13px;
    font-weight: 400;
    font-size: 16px;
    color: #A9C5C8;
  }

  .status-count {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 18px;
    color: #DAECF6;
  }

  &.green .status-dot {
    background-color: #09D8DB;
  }

  &.blue .status-dot {
    background-color: #218CDF;
  }

  &.orange .status-dot {
    background-color: #FCB24A;
  }

  &.red .status-dot {
    background-color: #FF744C;
  }
}

.chart-container {
  height: 180px;
}

// 最新告警设备样式
.latest-alarm-info {
  padding: 12px;
  height: calc(100% - 20px);
}

.alarm-info {
  flex: 1;
  margin-right: 12px;

  .alarm-time {
    font-size: 12px;
    color: #C0DAEA;
    margin-bottom: 4px;
  }

  .alarm-content {
    font-size: 13px;
    color: #FFFFFF;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }
}

.alarm-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.pending {
    background: rgba(255, 107, 107, 0.2);
    color: #FF6B6B;
    border: 1px solid rgba(255, 107, 107, 0.3);
  }

  &.processing {
    background: rgba(255, 165, 0, 0.2);
    color: #FFA500;
    border: 1px solid rgba(255, 165, 0, 0.3);
  }

  &.completed {
    background: rgba(0, 245, 255, 0.2);
    color: #00F5FF;
    border: 1px solid rgba(0, 245, 255, 0.3);
  }
}

.donghuan-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 12px 10px;
  background: url('@assets/images/line_img_1.png') no-repeat center center / 100% 100%;
  box-sizing: border-box;

  .donghuan-range {
    flex: 1;
    display: flex;
    align-items: center;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }

    .range-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;

      &.blue-dot {
        background: #40AAFC;
      }

      &.orange-dot {
        background: #FCB24A;
      }

      &.red-dot {
        background: #FD8D79;
      }
      
      &.yellow-dot {
        background: #FFD700;
      }
    }

    .range-text-title {
      font-weight: 500;
      font-size: 14px;
    }

    .range-text-value {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 18px;
    }

    .blue {
      color: #40AAFC;
    }

    .orange {
      color: #FCB24A;
    }

    .red {
      color: #FD8D79;
    }
    
    .yellow {
      color: #FFD700;
    }

    .range-text-unit {
      font-weight: 400;
      font-size: 14px;
      color: #97B0C8;
      margin-left: 4px;
    }
  }
}

.alarm-list-header {
  width: 100%;
  height: 44px;
  background: rgba(0, 127, 255, 0.06);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(42, 240, 254, 0.5)) 1 1;
  display: flex;
  align-items: center;
  padding: 0 10px;
  box-sizing: border-box;

  .alarm-list-header-item-title {
    font-weight: 500;
    font-size: 14px;
    color: #B2F3FF;

    &:nth-child(1) {
      flex: 0.4;
    }

    &:nth-child(2) {
      flex: 1;
    }

    &:nth-child(3) {
      flex: 1.3;
    }

    &:nth-child(4) {
      flex: 0.6;
    }
  }
}

.alarm-list {
  width: 100%;
  height: calc(100% - 110px);
  overflow: auto;

  .alarm-list-item {
    width: 100%;
    height: 38px;
    background: rgba(0, 127, 255, 0.06);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(42, 240, 254, 0.5)) 1 1;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    flex-shrink: 0;

    .alarm-list-item-title {
      font-weight: 500;
      font-size: 14px;
      color: #F8FEFF;

      &:nth-child(1) {
        flex: 0.4;
        font-family: DIN Alternate;
      }

      &:nth-child(2) {
        flex: 1;
        font-family: DIN Alternate;
      }

      &:nth-child(3) {
        flex: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:nth-child(4) {
        flex: 0.6;
      }
    }
  }

}

.pending {
  color: #FF6B6B !important;
}

.completed {
  color: #00F5FF !important;
}
</style>