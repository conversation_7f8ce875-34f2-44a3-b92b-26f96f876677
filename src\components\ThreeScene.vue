<template>
    <div class="three-scene-container">
        <div id="map"></div>
        <!-- 门禁列表 -->
        <div class="menjin-list" v-if="showMenjinList">
            <div class="menjin-header">
                <span class="menjin-title">{{ currentClickMarkerInfo.label || currentClickMarkerInfo.text }}</span>
                <img src="@/assets/images/close.png" alt="" srcset="" class="close-btn" @click="showMenjinList = false">
            </div>
            <div class="menjin-table">
                <div class="table-header">
                    <div class="th">时间</div>
                    <div class="th">姓名</div>
                    <div class="th">工号</div>
                    <div class="th">门点</div>
                    <div class="th">方向</div>
                    <div class="th">状态</div>
                </div>
                <div class="table-body">
                    <div class="tr" v-for="(record, index) in menjinRecords" :key="index">
                        <div class="td">{{ record.time }}</div>
                        <div class="td">{{ record.name }}</div>
                        <div class="td">{{ record.workId }}</div>
                        <div class="td">{{ record.location }}</div>
                        <div class="td">{{ record.direction }}</div>
                        <div class="td">
                            <div class="status" :class="record.status === '正常' ? 'status-normal' : 'status-reject'">
                            </div>{{
                                record.status }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备详情 -->
        <div class="device-detail" v-if="showDeviceDetail">
            <div class="menjin-header">
                <span class="menjin-title">{{ currentClickMarkerInfo.label || currentClickMarkerInfo.text }}</span>
                <img src="@/assets/images/close.png" alt="" srcset="" class="close-btn"
                    @click="showDeviceDetail = false">
            </div>
            <div class="device-info" v-if="!isLoadingDeviceData">
                <div class="info-row" v-for="(row, index) in deviceInfoRows" :key="index">
                    <div class="info-item" v-for="(item, itemIndex) in row" :key="itemIndex">
                        <div class="info-block"></div>
                        <span class="info-label">{{ item.label }}：</span>
                        <span class="info-value">{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <div class="device-info loading" v-else>
                <div class="loading-text">加载中...</div>
            </div>
        </div>

        <!-- 视频播放弹窗 -->
        <div class="video-modal" v-if="showVideoModal">
            <div class="video-container">
                <div class="menjin-header">
                    <span class="menjin-title">{{ currentClickMarkerInfo.label || currentClickMarkerInfo.text }}</span>
                    <img src="@/assets/images/close.png" alt="" srcset="" class="close-btn" @click="closeVideoModal">
                </div>
                <div class="video-player">
                    <TpCellPlayer :cell="{ url: videoPlayerUrl }" v-if="videoPlayerUrl" />
                    <div class="video-loading" v-if="isVideoLoading">
                        <div class="loading-text">视频加载中...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 悬浮提示框 - 暂时注释 -->
        <!-- <div class="hover-tooltip" v-if="tooltip.show" :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }">
            {{ tooltip.content }}
        </div> -->
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, nextTick } from 'vue';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { get } from '@/api/request.js';
import TpCellPlayer from './TpCellPlayer.vue';

let scene, camera, renderer, controls, model, labelRenderer;
let animationId;
let clickListenerCleanup; // 保存点击事件监听器的清理函数

const showMenjinList = ref(false);
const showDeviceDetail = ref(false);
const currentClickMarkerInfo = ref({});

const deviceDetail = ref({});
const deviceListData = ref([]);
const isLoadingDeviceData = ref(false);

// 视频相关状态
const showVideoModal = ref(false);
const videoPlayerUrl = ref('');
const isVideoLoading = ref(false);

// 摄像头URL映射
const cameraUrlMap = {
    '摄像头1': 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=54925bf108de411698d05ad3e27cdc54',
    '摄像头2': 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=31a74893a08541c3ad553523888ba684',
    '摄像头3': 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=54925bf108de411698d05ad3e27cdc54',
    '摄像头4': 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=31a74893a08541c3ad553523888ba684',
    '摄像头5': 'https://smart.building.zjsjt.com.cn:8099/admin-api/smartcarbon/zjmanage/largescreen/video/v1/cameras/previewURLs?cameraIndexCode=54925bf108de411698d05ad3e27cdc54'
};

// 悬停相关状态
const currentHoveredObject = ref(null);

// 机柜选中状态
const selectedCabinetKey = ref(null);

// 悬浮tooltip相关状态
const tooltip = ref({
    show: false,
    content: '',
    x: 0,
    y: 0
});

// 全局鼠标坐标追踪
const globalMousePosition = ref({
    x: 0,
    y: 0
});

/**
 * 获取页面的缩放比例
 * @returns {number} 缩放比例
 */
function getPageScaleRatio() {
    const app = document.getElementById('app');
    if (!app) return 1;
    
    const transform = getComputedStyle(app).transform;
    if (transform === 'none') return 1;
    
    // 解析 transform matrix 获取缩放比例
    const matrix = transform.match(/matrix\(([^)]+)\)/);
    if (matrix) {
        const values = matrix[1].split(',').map(v => parseFloat(v.trim()));
        return values[0]; // scaleX
    }
    
    // 或者直接根据 flexible-dashboard.js 的逻辑计算
    const baseHeight = document.documentElement.clientHeight;
    return baseHeight / 1080;
}

/**
 * 根据页面缩放调整鼠标坐标
 * @param {number} clientX - 原始clientX
 * @param {number} clientY - 原始clientY
 * @returns {Object} 调整后的坐标 {x, y}
 */
function adjustMouseCoordinates(clientX, clientY) {
    const scaleRatio = getPageScaleRatio();
    const app = document.getElementById('app');
    
    if (!app || scaleRatio === 1) {
        return { x: clientX, y: clientY };
    }
    
    // 获取app元素的位置信息
    const appRect = app.getBoundingClientRect();
    
    // 计算相对于app元素的坐标
    const relativeX = clientX - appRect.left;
    const relativeY = clientY - appRect.top;
    
    // 根据缩放比例调整坐标
    const adjustedX = relativeX / scaleRatio + appRect.left;
    const adjustedY = relativeY / scaleRatio + appRect.top;
    
    return { x: adjustedX, y: adjustedY };
}

/**
 * 设备名称映射转换函数
 * @param {string} originalName - 模型原始名称
 * @returns {string} - 转换后的友好显示名称
 */
function mapDeviceName(originalName) {
    if (!originalName) return '';
    
    // console.log('🔄 原始设备名称:', originalName);
    
    // A0_(1)-A9_(1) 转换为 A0柜-A9柜
    const aRackMatch = originalName.match(/^(A\d+)(_\(\d+\))?/);
    if (aRackMatch) {
        const rackNumber = aRackMatch[1];
        const displayName = `${rackNumber}柜`;
        // console.log('🔄 A系列机柜转换:', originalName, '->', displayName);
        return displayName;
    }
    
    // B0_(1)-B9_(1) 转换为 B0柜-B9柜
    const bRackMatch = originalName.match(/^(B\d+)(_\(\d+\))?/);
    if (bRackMatch) {
        const rackNumber = bRackMatch[1];
        const displayName = `${rackNumber}柜`;
        console.log('🔄 B系列机柜转换:', originalName, '->', displayName);
        return displayName;
    }
    
    // Box192、Box193 转换为 机房空调
    if (originalName === 'Box192' || originalName === 'Box193') {
        const displayName = '机房空调';
        console.log('🔄 空调设备转换:', originalName, '->', displayName);
        return displayName;
    }
    
    // Box194、Box195、Box196 转换为 配电柜
    if (originalName === 'Box194' || originalName === 'Box195' || originalName === 'Box196') {
        const displayName = '配电柜';
        console.log('🔄 配电柜设备转换:', originalName, '->', displayName);
        return displayName;
    }
    
    // 机柜4、机柜008 转换为 UPS
    if (originalName === '机柜4' || originalName === '机柜008') {
        const displayName = 'UPS';
        console.log('🔄 UPS设备转换:', originalName, '->', displayName);
        return displayName;
    }
    
    // 如果没有匹配的映射规则，返回原始名称
    console.log('🔄 无映射规则，保持原名:', originalName);
    return originalName;
}

// 设备状态映射
const deviceStatusMappings = {
    // 空调状态映射
    airConditioner: {
        0: '关机',
        1: '运行',
        2: '待机',
        4: '本地关机',
        8: '远程关机',
        16: '监控关机'
    },
    // UPS状态映射
    ups: {
        0: '初始化',
        1: '待机状态',
        2: '无输出状态',
        3: '旁路状态',
        4: '市电状态',
        5: '电池状态',
        6: '电池自检状态',
        7: '逆变启动中',
        8: '经济模式',
        9: 'EPO状态',
        10: '维护旁路模式',
        11: '故障模式',
        12: 'UPS重新初始化状态',
        13: '联合供电'
    }
};

// 根据设备名称判断设备类型
function getDeviceType(deviceName) {
    if (!deviceName) return 'unknown';

    const name = deviceName.toLowerCase();

    // 判断是否是空调类设备
    if (name.includes('空调') || name.includes('列间') || name.includes('配电间') ||
        name.includes('air') || name.includes('hvac')) {
        return 'airConditioner';
    }

    // 判断是否是UPS设备
    if (name.includes('ups') || name.includes('不间断电源') ||
        name.includes('uninterruptible') || name.includes('电源')) {
        return 'ups';
    }

    return 'unknown';
}

// 格式化参数值
function formatParameterValue(param, deviceName) {
    const signalName = param.signalName || '';
    const currentValue = param.currentValue;
    const unit = param.unit || '';

    // 判断是否是状态参数
    if (signalName.includes('状态') || signalName.includes('State') || signalName.toLowerCase().includes('status')) {
        // 获取设备类型
        const deviceType = getDeviceType(deviceName);
        // 根据设备类型获取对应的状态映射
        let statusMapping = null;
        if (deviceType === 'airConditioner') {
            statusMapping = deviceStatusMappings.airConditioner;
        } else if (deviceType === 'ups') {
            statusMapping = deviceStatusMappings.ups;
        }

        // 如果有状态映射，则转换状态值
        if (statusMapping && statusMapping.hasOwnProperty(currentValue)) {
            const statusText = statusMapping[currentValue];
            return statusText;
        } else if (statusMapping) {
            return `未知状态(${currentValue})`;
        }
    }

    // 如果不是状态参数或没有对应的映射，返回原始值加单位
    return `${currentValue}${unit}`;
}

// 将设备详情数据按每行3个分组
const deviceInfoRows = computed(() => {
    const canshuData = deviceDetail.value.canshu || [];

    // 如果没有参数数据，显示暂无数据
    if (canshuData.length === 0) {
        return [[{ label: '暂无数据', value: '-' }]];
    }

    // 格式化参数数据
    const items = canshuData.map(param => ({
        label: param.signalName,
        value: formatParameterValue(param, deviceDetail.value.deviceName)
    }));

    const rows = [];
    for (let i = 0; i < items.length; i += 3) {
        rows.push(items.slice(i, i + 3));
    }
    return rows;
});

// 门禁记录数据
const menjinRecords = ref([
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '正常' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '拒绝' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '正常' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '正常' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '拒绝' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '拒绝' },
    { time: '2022.09.08 17:08', name: '张三', workId: '1001', location: 'A机房南门', direction: '进入', status: '正常' },
]);

// 定义一些机柜点位（这些坐标需要根据你的实际机柜位置调整）
const points = [
    // { position: [3.063, 3.996, -2.788], label: '门禁', color: '#ffffff', type: '门禁' },
    {
        position: [2.871, 1.270, 0.728],
        rotation: [0.000, 4.712, 0.000],
        label: '前门指纹机',
        color: '#ffffff',
        type: '前门指纹机',
        modelName: 'MJ_001'
    },
    {
        position: [2.897, 2.202, 1.607],
        label: '摄像头1',
        color: '#ffffff',
        type: '摄像头',
        useSprite: true,
        modelName: 'SXT_001'
    },
    {
        position: [-4.504, 3.719, -2.891],
        label: '摄像头2',
        color: '#ffffff',
        type: '摄像头',
        useSprite: true,
        modelName: 'SXT_005'
    },
    {
        position: [-4.580, 3.716, 4.196],
        label: '摄像头3',
        color: '#ffffff',
        type: '摄像头',
        useSprite: true,
        modelName: 'SXT_004'
    },
    {
        position: [-2.054, 3.760, 6.409],
        label: '摄像头4',
        color: '#ffffff',
        type: '摄像头',
        useSprite: true,
        modelName: 'SXT_003'
    },
    {
        position: [-4.352, 3.969, -5.490],
        label: '摄像头5',
        color: '#ffffff',
        type: '摄像头',
        useSprite: true,
        modelName: 'SXT_006'
    },
    {
        position: [-4.167, 2.807, -3.856],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '配电间1号空调',
        color: '#ffffff',
        type: '配电间空调',
        modelName: 'JFKT_002'
    },
    {
        position: [-4.288, 2.807, -5.705],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '配电间2号空调',
        color: '#ffffff',
        type: '配电间空调',
        modelName: 'JFKT_001'
    },
    // { position: [-4.23, 2.231, -1.563], label: '烟感', color: '#ffffff', type: '烟感' },
    // { position: [-2.996, 2.423, 1.793], label: '精密列头柜', color: '#ffffff', type: '精密列头柜' },
    { position: [2.423, 2.23, 0.452], rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2], label: '精密列头柜', color: '#ffffff', type: '精密列头柜' },
    // { position: [-1.282, 2.229, 0.031], label: '机房空调', color: '#ffffff', type: '机房空调' },
    // { position: [-2.045, 1.005, -4.063], label: '温湿度设备', color: '#ffffff', type: '温湿度设备' },
    {
        position: [-1.490, 2.509, 2.671],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '3号列间空调',
        color: '#ffffff',
        type: '3号列间空调',
        modelName: 'LJKT_003'
    },
    {
        position: [-3.324, 1.485, 2.339],
        rotation: [0.000, 1.571, 0.000],
        label: '后门指纹机',
        color: '#ffffff',
        type: '后门指纹机',
        modelName: 'MJ_002'
    },
    // { 
    //     position: [0.844, 1.132, -4.794], 
    //     rotation: [-Math.PI / 180 * 94, 0, 
    //     Math.PI / 2], label: '2号UEUPS', 
    //     color: '#ffffff', 
    //     type: 'UPS',
    //     modelName: 'USP标签'
    // },
    { 
        position: [0.197, 1.132, -4.771], 
        rotation: [-Math.PI / 180 * 94, 0, 
        Math.PI / 2], 
        label: '1号UEUPS', 
        color: '#ffffff', 
        type: 'UPS',
        modelName: 'USP标签'
    },
    {
        position: [1.284, 2.509, 2.790],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '1号列间空调',
        color: '#ffffff',
        type: '1号列间空调',
        modelName: 'LJKT_002'
    },
    {
        position: [0.763, 2.509, 0.553],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '2号列间空调',
        color: '#ffffff',
        type: '2号列间空调',
        modelName: 'LJKT_001'
    },
    {
        position: [0.668, 2.807, -6.046],
        rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
        label: '配电柜',
        color: '#ffffff',
        type: '配电柜',
        modelName: 'PDG_001,PDG_002,PDG_003'
    },
    // {
    //     position: [-0.131, 2.224, -6.003],
    //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
    //     label: 'UPS输出柜',
    //     color: '#ffffff',
    //     type: 'UPS输出柜'
    // },
    // {
    //     position: [0.590, 2.224, -6.003],
    //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
    //     label: '市电输出柜',
    //     color: '#ffffff',
    //     type: '市电输出柜'
    // },
    // {
    //     position: [1.611, 2.224, -6.003],
    //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
    //     label: '市电总切换柜',
    //     color: '#ffffff',
    //     type: '市电总切换柜'
    // },
];

onMounted(async () => {
    await initThreeScene();

    // 获取设备数据
    await fetchDeviceData();

    // 添加全局鼠标位置追踪
    const handleGlobalMouseMove = (event) => {
        // 根据页面缩放调整鼠标坐标
        const adjustedCoords = adjustMouseCoordinates(event.clientX, event.clientY);
        globalMousePosition.value.x = adjustedCoords.x;
        globalMousePosition.value.y = adjustedCoords.y;
        console.log('🔄 原始坐标:', event.clientX, event.clientY, '调整后坐标:', adjustedCoords.x, adjustedCoords.y, '缩放比例:', getPageScaleRatio());
    };
    window.addEventListener('mousemove', handleGlobalMouseMove);
    // 保存清理函数到window，方便在onUnmounted中清理
    window._globalMouseTracker = handleGlobalMouseMove;

    // 监听设备选择变化事件
    window.addEventListener('deviceSelectionChanged', handleDeviceSelectionChanged);

    // 监听设备选择事件
    window.addEventListener('deviceSelected', handleDeviceSelected);

    window.addHighTemperatureEffect = addHighTemperatureEffect;
    // window.addCabinetMarkers = addCabinetMarkers;
    window.addStorageSpaceEffect = addStorageSpaceEffect;
    window.addManyou = addManyou;
    window.clearScene = clearScene;
    window.showAllDeviceLabels = showAllDeviceLabels;
    window.hideAllDeviceLabels = hideAllDeviceLabels;
    window.addHeatmap = addHeatmap;
    window.getCabinetViewKey = getCabinetViewKey;
    window.resetToInitialView = resetToInitialView;
    
    // 添加机柜设备操作测试函数
    window.testCabinetDeviceSequence = testCabinetDeviceSequence;
});

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    if (renderer) {
        renderer.dispose();
    }
    if (labelRenderer) {
        labelRenderer.domElement.remove();
    }
    if (controls) {
        controls.dispose();
    }

    // 清除机柜选中状态
    selectedCabinetKey.value = null;

    // 移除全局鼠标位置追踪
    if (window._globalMouseTracker) {
        window.removeEventListener('mousemove', window._globalMouseTracker);
        delete window._globalMouseTracker;
    }

    // 移除事件监听
    window.removeEventListener('deviceSelectionChanged', handleDeviceSelectionChanged);
    window.removeEventListener('deviceSelected', handleDeviceSelected);
    
    // 清理点击事件监听器
    if (clickListenerCleanup) {
        clickListenerCleanup();
    }

    // 清理悬停效果
    if (window.threeDebug && window.threeDebug.clearHoverEffects) {
        window.threeDebug.clearHoverEffects();
    }

    // 清理全局方法引用
    delete window.addHighTemperatureEffect;
    // delete window.addCabinetMarkers;
    delete window.addStorageSpaceEffect;
    delete window.addManyou;
    delete window.clearScene;
    delete window.showAllDeviceLabels;
    delete window.hideAllDeviceLabels;
    delete window.addHeatmap;
    delete window.getCabinetViewKey;
    delete window.resetToInitialView;
    delete window.testCabinetDeviceSequence;
    delete window.threeDebug;
});

// 获取设备数据
async function fetchDeviceData() {
    try {
        isLoadingDeviceData.value = true;
        
        // 直接请求获取设备数据
        const response = await fetch('http://47.92.199.181:8099/api/zjRotatingRing/devicelist?remark=A');
        const result = await response.json();

        if (result.code === 0 && result.data) {
            deviceListData.value = result.data;
            console.log('设备数据加载成功:', deviceListData.value);
        } else {
            console.warn('获取设备数据失败，响应:', result);
            deviceListData.value = [];
        }
    } catch (error) {
        console.error('获取设备数据失败:', error);
        deviceListData.value = [];
    } finally {
        isLoadingDeviceData.value = false;
    }
}

async function initThreeScene() {
    const container = document.getElementById('map');
    if (!container) return;

    // 创建场景
    scene = new THREE.Scene();

    // 创建相机
    camera = new THREE.PerspectiveCamera(
        75,
        container.clientWidth / container.clientHeight,
        0.1,
        1000
    );
    camera.position.set(3.890274067613357, 9.352182239442273, -0.08217628147574355);

    // 创建WebGL渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true, autoClear: true });
    renderer.autoClear = true;
    renderer.setClearColor(0x000000, 0); // 设置透明背景
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.sortObjects = true; // 启用对象排序，有助于透明度和层级渲染
    container.appendChild(renderer.domElement);

    // 创建CSS2D渲染器
    labelRenderer = new CSS2DRenderer();
    labelRenderer.setSize(container.clientWidth, container.clientHeight);
    labelRenderer.domElement.style.position = 'absolute';
    labelRenderer.domElement.style.top = '0px';
    labelRenderer.domElement.style.pointerEvents = 'none';
    container.appendChild(labelRenderer.domElement);

    // 创建控制器
    controls = _initControl();

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
    directionalLight.position.set(50, 50, 0);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    let hemisphereLight = new THREE.HemisphereLight(0xffffff, 0x444444, 1.6);
    scene.add(hemisphereLight);

    // // 在场景原点添加坐标轴辅助器
    // const axesHelper = new THREE.AxesHelper(10); // 参数是轴的长度
    // axesHelper.position.set(0, 0, 0); // 确保在原点
    // scene.add(axesHelper);
    // console.log('已在原点添加坐标轴辅助器');

    // 加载GLB模型
    const loader = new GLTFLoader();
    const gltf = await new Promise((resolve, reject) => {
        // loader.load('./model/JF_6-28bl.glb', resolve, undefined, reject);
        loader.load('./model/jifang5.glb', resolve, undefined, reject);
    });
    model = gltf.scene;
    // 计算模型边界盒
    const box = new THREE.Box3().setFromObject(model);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    model.position.set(-center.x, -center.y + size.y / 2, -center.z);

    // 启用阴影
    model.traverse((child) => {
        // console.log(child);

        if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
        }
    });

    scene.add(model);

    // // 添加机柜标记点位
    // addCabinetMarkers();



    // 将关键对象暴露到window上，方便控制台调试
    window.threeDebug = {
        scene,
        camera,
        renderer,
        controls,
        model,
        // ... existing code ...
        clearTempMarkers: () => {
            const tempMarkers = scene.children.filter(child =>
                child.userData && (child.userData.type === 'temp-marker' || child.userData.type === 'temp-marker-label')
            );
            tempMarkers.forEach(marker => {
                scene.remove(marker);
                if (marker.geometry) marker.geometry.dispose();
                if (marker.material) {
                    if (marker.material.map) marker.material.map.dispose();
                    marker.material.dispose();
                }
                if (marker.userData.texture) marker.userData.texture.dispose();
                if (marker.userData.geometry) marker.userData.geometry.dispose();
            });
            console.log(`✅ 清除了 ${tempMarkers.length} 个临时标记`);
        },
        // 获取所有构件名称
        getModelObjects() {
            const objects = [];
            if (model) {
                model.traverse((child) => {
                    if (child.isMesh && child.name) {
                        objects.push({
                            name: child.name,
                            type: child.type,
                            visible: child.visible,
                            position: {
                                x: child.position.x.toFixed(3),
                                y: child.position.y.toFixed(3),
                                z: child.position.z.toFixed(3)
                            }
                        });
                    }
                });
            }
            console.log('模型中的构件:', objects);
            return objects;
        },
        // 隐藏指定名称的构件
        hideObject(objectName) {
            if (!model || !objectName) {
                console.warn('模型未加载或构件名称为空');
                return false;
            }
            
            let found = false;
            model.traverse((child) => {
                if (child.name === objectName) {
                    child.visible = false;
                    found = true;
                    console.log(`✅ 已隐藏构件: ${objectName}`);
                }
            });
            
            if (!found) {
                console.warn(`❌ 未找到构件: ${objectName}`);
            }
            return found;
        },
        // 显示指定名称的构件
        showObject(objectName) {
            if (!model || !objectName) {
                console.warn('模型未加载或构件名称为空');
                return false;
            }
            
            let found = false;
            model.traverse((child) => {
                if (child.name === objectName) {
                    child.visible = true;
                    found = true;
                    console.log(`✅ 已显示构件: ${objectName}`);
                }
            });
            
            if (!found) {
                console.warn(`❌ 未找到构件: ${objectName}`);
            }
            return found;
        },
        // 批量隐藏构件
        hideObjects(objectNames) {
            if (!Array.isArray(objectNames)) {
                console.warn('参数必须是数组');
                return;
            }
            
            const results = objectNames.map(name => ({
                name,
                success: this.hideObject(name)
            }));
            
            console.log('批量隐藏结果:', results);
            return results;
        },
        // 批量显示构件
        showObjects(objectNames) {
            if (!Array.isArray(objectNames)) {
                console.warn('参数必须是数组');
                return;
            }
            
            const results = objectNames.map(name => ({
                name,
                success: this.showObject(name)
            }));
            
            console.log('批量显示结果:', results);
            return results;
        },
        // 根据名称模糊匹配隐藏构件
        hideObjectsByPattern(pattern) {
            if (!model || !pattern) {
                console.warn('模型未加载或匹配模式为空');
                return;
            }
            
            const hiddenObjects = [];
            model.traverse((child) => {
                if (child.name && child.name.includes(pattern)) {
                    child.visible = false;
                    hiddenObjects.push(child.name);
                }
            });
            
            console.log(`✅ 通过模式 "${pattern}" 隐藏了 ${hiddenObjects.length} 个构件:`, hiddenObjects);
            return hiddenObjects;
        },
        // 显示所有构件
        showAllObjects() {
            if (!model) {
                console.warn('模型未加载');
                return;
            }
            
            let count = 0;
            model.traverse((child) => {
                if (child.isMesh && !child.visible) {
                    child.visible = true;
                    count++;
                }
            });
            
            console.log(`✅ 已显示所有构件，共 ${count} 个`);
            return count;
        }
    };

    // 开始动画循环
    animate();

    // 处理窗口大小变化
    window.addEventListener('resize', onWindowResize);

    // 添加点击事件监听
    clickListenerCleanup = addClickListener();

    // 添加鼠标悬停事件监听
    addHoverListener();
    window.dispatchEvent(new CustomEvent('threeSceneReady'));
    window.threeDebug.hideObjects(['GX'])
    // 显示rotation获取工具的使用说明
    setTimeout(() => {
        if (window.threeDebug && window.threeDebug.showRotationHelp) {
            window.threeDebug.showRotationHelp();
        }
    }, 1000);
}

// 初始化控制器
function _initControl() {
    const control = new OrbitControls(
        camera,
        renderer.domElement
    );

    control.target = new THREE.Vector3(-0.6548553429473142, 0.11413210465790953, -0.06706507811251107);
    control.enableDamping = true;
    control.dampingFactor = 0.1;
    control.update();

    // control.addEventListener("change", () => {
    //     console.log('相机位置变化:', {
    //         position: {
    //             x: camera.position.x,
    //             y: camera.position.y,
    //             z: camera.position.z
    //         },
    //         target: {
    //             x: control.target.x,
    //             y: control.target.y,
    //             z: control.target.z
    //         }
    //     });
    // });

    return control;
}

function animate() {
    animationId = requestAnimationFrame(animate);

    if (controls) {
        controls.update();
    }

    if (renderer && scene && camera) {
        renderer.render(scene, camera);
        labelRenderer.render(scene, camera);
    }
}

function onWindowResize() {
    const container = document.getElementById('map');
    if (container && camera && renderer && labelRenderer) {
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(container.clientWidth, container.clientHeight);
        labelRenderer.setSize(container.clientWidth, container.clientHeight);
    }
}

// 创建文字平面标签
function createPlaneLabel(text, color = '#ffffff', type) {
    // 创建canvas纹理
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // 获取设备像素比，提高高DPI屏幕的渲染质量
    const pixelRatio = window.devicePixelRatio || 1;

    // 设置高分辨率canvas大小
    const baseWidth = 512;
    const baseHeight = 128;
    canvas.width = baseWidth * pixelRatio;
    canvas.height = baseHeight * pixelRatio;

    // 设置CSS样式大小
    canvas.style.width = baseWidth + 'px';
    canvas.style.height = baseHeight + 'px';

    // 缩放绘图上下文以匹配设备像素比
    context.scale(pixelRatio, pixelRatio);

    // 设置高质量渲染
    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = 'high';
    context.textRenderingOptimization = 'optimizeQuality';

    // 设置字体样式 - 使用指定的字体规范
    const fontSize = 72; // 增大字体以确保更好的可见性
    context.font = `normal 400 ${fontSize}px YouSheBiaoTiHei, Arial, sans-serif`;
    context.fillStyle = '#FFFFFF';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    // 设置字符间距（通过手动计算实现letter-spacing效果）

    // 绘制背景（半透明圆角矩形）
    const padding = 20;
    const textWidth = context.measureText(text).width;
    const bgWidth = textWidth + padding * 2;
    const bgHeight = 80;
    const bgX = (baseWidth - bgWidth) / 2;
    const bgY = (baseHeight - bgHeight) / 2;

    // 绘制背景 - 使用线性渐变 (271deg)
    // 计算271度角的渐变方向
    const angle = (271 * Math.PI) / 180; // 转换为弧度
    const gradientLength = Math.sqrt(bgWidth * bgWidth + bgHeight * bgHeight);
    const centerX = bgX + bgWidth / 2;
    const centerY = bgY + bgHeight / 2;
    const x1 = centerX - (gradientLength / 2) * Math.cos(angle);
    const y1 = centerY - (gradientLength / 2) * Math.sin(angle);
    const x2 = centerX + (gradientLength / 2) * Math.cos(angle);
    const y2 = centerY + (gradientLength / 2) * Math.sin(angle);

    const bgGradient = context.createLinearGradient(x1, y1, x2, y2);
    bgGradient.addColorStop(0, 'rgba(39, 171, 255, 0.28)'); // 271deg起始色
    bgGradient.addColorStop(1, 'rgba(33, 115, 238, 0.35)'); // 271deg结束色

    context.fillStyle = bgGradient;
    context.beginPath();

    // 绘制圆角矩形（兼容性处理）
    const radius = 4;
    if (context.roundRect) {
        context.roundRect(bgX, bgY, bgWidth, bgHeight, radius);
    } else {
        // 手动绘制圆角矩形
        context.moveTo(bgX + radius, bgY);
        context.lineTo(bgX + bgWidth - radius, bgY);
        context.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
        context.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
        context.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
        context.lineTo(bgX + radius, bgY + bgHeight);
        context.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
        context.lineTo(bgX, bgY + radius);
        context.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
    }

    context.fill();

    // 绘制边框 - 使用纯色
    context.strokeStyle = 'rgba(39, 171, 255, 0.24)'; // 27ABFF 24%
    context.lineWidth = 10;

    // 重新创建路径用于边框绘制
    context.beginPath();
    if (context.roundRect) {
        context.roundRect(bgX, bgY, bgWidth, bgHeight, radius);
    } else {
        // 手动绘制圆角矩形
        context.moveTo(bgX + radius, bgY);
        context.lineTo(bgX + bgWidth - radius, bgY);
        context.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
        context.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
        context.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
        context.lineTo(bgX + radius, bgY + bgHeight);
        context.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
        context.lineTo(bgX, bgY + radius);
        context.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
    }
    context.stroke();

    // 绘制文字 - 使用指定的颜色和字符间距
    context.fillStyle = '#FFFFFF';

    // 添加文字阴影效果，增强可读性
    context.shadowColor = 'rgba(0, 0, 0, 0.8)';
    context.shadowBlur = 3;
    context.shadowOffsetX = 1;
    context.shadowOffsetY = 1;

    // 实现letter-spacing: 1px的效果
    const letterSpacing = 1;
    if (letterSpacing > 0 && text.length > 1) {
        // 手动绘制每个字符以实现字符间距
        const totalWidth = context.measureText(text).width + (text.length - 1) * letterSpacing;
        let currentX = (baseWidth - totalWidth) / 2;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const charWidth = context.measureText(char).width;
            context.fillText(char, currentX + charWidth / 2, baseHeight / 2);
            currentX += charWidth + letterSpacing;
        }
    } else {
        // 普通绘制（无字符间距或单字符）
        context.fillText(text, baseWidth / 2, baseHeight / 2);
    }

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 创建平面几何体
    const planeGeometry = new THREE.PlaneGeometry(2, 0.5);

    // 创建平面材质
    const planeMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1,
        depthTest: false,   // 启用深度测试，保持正常3D关系
        depthWrite: false, // 禁用深度写入，避免影响其他对象
        polygonOffset: true,     // 启用多边形偏移
        polygonOffsetFactor: -1, // 偏移因子
        polygonOffsetUnits: -1,  // 偏移单位
        side: THREE.DoubleSide,   // 双面渲染，确保从任何角度都能看到
        depthTest: true,
    });

    // 创建平面网格
    const planeMesh = new THREE.Mesh(planeGeometry, planeMaterial);
    planeMesh.renderOrder = 999; // 设置高渲染优先级，确保在同深度时优先显示
    planeMesh.userData = {
        type: 'plane-label',
        text: text,
        deviceType: type,
        canvas: canvas,
        texture: texture,
        material: planeMaterial,
        geometry: planeGeometry
    };

    return planeMesh;
}

// 创建点精灵标签（用于摄像头等设备）
function createSpriteLabel(text, color = '#ffffff', type) {
    // 创建canvas纹理
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // 获取设备像素比，提高高DPI屏幕的渲染质量
    const pixelRatio = window.devicePixelRatio || 1;

    // 设置高分辨率canvas大小 - 使用和平面标签相同的比例
    const baseWidth = 512;
    const baseHeight = 128;
    canvas.width = baseWidth * pixelRatio;
    canvas.height = baseHeight * pixelRatio;

    // 设置CSS样式大小
    canvas.style.width = baseWidth + 'px';
    canvas.style.height = baseHeight + 'px';

    // 缩放绘图上下文以匹配设备像素比
    context.scale(pixelRatio, pixelRatio);

    // 设置高质量渲染
    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = 'high';
    context.textRenderingOptimization = 'optimizeQuality';

    // 设置字体样式 - 和平面标签相同
    const fontSize = 64; // 增大字体以确保更好的可见性
    context.font = `normal 400 ${fontSize}px YouSheBiaoTiHei, Arial, sans-serif`;
    context.fillStyle = '#FFFFFF';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    // 设置字符间距（通过手动计算实现letter-spacing效果）

    // 绘制背景（半透明圆角矩形） - 和平面标签相同的样式
    const padding = 20;
    const textWidth = context.measureText(text).width;
    const bgWidth = textWidth + padding * 2;
    const bgHeight = 80;
    const bgX = (baseWidth - bgWidth) / 2;
    const bgY = (baseHeight - bgHeight) / 2;

    // 绘制背景 - 使用线性渐变 (271deg)
    // 计算271度角的渐变方向
    const spriteAngle = (271 * Math.PI) / 180; // 转换为弧度
    const spriteGradientLength = Math.sqrt(bgWidth * bgWidth + bgHeight * bgHeight);
    const spriteCenterX = bgX + bgWidth / 2;
    const spriteCenterY = bgY + bgHeight / 2;
    const spriteX1 = spriteCenterX - (spriteGradientLength / 2) * Math.cos(spriteAngle);
    const spriteY1 = spriteCenterY - (spriteGradientLength / 2) * Math.sin(spriteAngle);
    const spriteX2 = spriteCenterX + (spriteGradientLength / 2) * Math.cos(spriteAngle);
    const spriteY2 = spriteCenterY + (spriteGradientLength / 2) * Math.sin(spriteAngle);

    const bgGradient = context.createLinearGradient(spriteX1, spriteY1, spriteX2, spriteY2);
    bgGradient.addColorStop(0, 'rgba(39, 171, 255, 0.28)'); // 271deg起始色
    bgGradient.addColorStop(1, 'rgba(33, 115, 238, 0.35)'); // 271deg结束色

    context.fillStyle = bgGradient;
    context.beginPath();

    // 绘制圆角矩形（兼容性处理）
    const radius = 4;
    if (context.roundRect) {
        context.roundRect(bgX, bgY, bgWidth, bgHeight, radius);
    } else {
        // 手动绘制圆角矩形
        context.moveTo(bgX + radius, bgY);
        context.lineTo(bgX + bgWidth - radius, bgY);
        context.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
        context.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
        context.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
        context.lineTo(bgX + radius, bgY + bgHeight);
        context.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
        context.lineTo(bgX, bgY + radius);
        context.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
    }

    context.fill();

    // 绘制边框 - 使用纯色
    context.strokeStyle = 'rgba(39, 171, 255, 0.24)'; // 27ABFF 24%
    context.lineWidth = 4;

    // 重新创建路径用于边框绘制
    context.beginPath();
    if (context.roundRect) {
        context.roundRect(bgX, bgY, bgWidth, bgHeight, radius);
    } else {
        // 手动绘制圆角矩形
        context.moveTo(bgX + radius, bgY);
        context.lineTo(bgX + bgWidth - radius, bgY);
        context.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
        context.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
        context.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
        context.lineTo(bgX + radius, bgY + bgHeight);
        context.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
        context.lineTo(bgX, bgY + radius);
        context.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
    }
    context.stroke();

    // 绘制文字 - 使用指定的颜色和字符间距
    context.fillStyle = '#FFFFFF';

    // 添加文字阴影效果，增强可读性
    context.shadowColor = 'rgba(0, 0, 0, 0.8)';
    context.shadowBlur = 3;
    context.shadowOffsetX = 1;
    context.shadowOffsetY = 1;

    // 实现letter-spacing: 1px的效果
    const letterSpacing = 1;
    if (letterSpacing > 0 && text.length > 1) {
        // 手动绘制每个字符以实现字符间距
        const totalWidth = context.measureText(text).width + (text.length - 1) * letterSpacing;
        let currentX = (baseWidth - totalWidth) / 2;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const charWidth = context.measureText(char).width;
            context.fillText(char, currentX + charWidth / 2, baseHeight / 2);
            currentX += charWidth + letterSpacing;
        }
    } else {
        // 普通绘制（无字符间距或单字符）
        context.fillText(text, baseWidth / 2, baseHeight / 2);
    }

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 创建点精灵材质
    const spriteMaterial = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1,
        depthTest: true,
        depthWrite: false,
        sizeAttenuation: true // 启用距离衰减，远处的精灵会变小
    });

    // 创建精灵
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(2, 0.5, 1); // 设置精灵大小，保持和平面标签相同的宽高比
    sprite.userData = {
        type: 'sprite-label',
        text: text,
        deviceType: type,
        canvas: canvas,
        texture: texture,
        material: spriteMaterial,
        isClickable: true
    };

    return sprite;
}

// 添加机柜标记点位
function addCabinetMarkers() {
    // 定义一些机柜点位（这些坐标需要根据你的实际机柜位置调整）
    const cabinetPoints = [
        // { position: [3.063, 3.996, -2.788], label: '门禁', color: '#ffffff', type: '门禁' },
        {
            position: [2.871, 1.270, 0.728],
            rotation: [0.000, 4.712, 0.000],
            label: '前门指纹机',
            color: '#ffffff',
            type: '前门指纹机',
            modelName: 'MJ_001'
        },
        {
            position: [2.897, 2.202, 1.607],
            label: '摄像头1',
            color: '#ffffff',
            type: '摄像头',
            useSprite: true,
            modelName: 'SXT_001'
        },
        {
            position: [-4.504, 3.719, -2.891],
            label: '摄像头2',
            color: '#ffffff',
            type: '摄像头',
            useSprite: true,
            modelName: 'SXT_005'
        },
        {
            position: [-4.580, 3.716, 4.196],
            label: '摄像头3',
            color: '#ffffff',
            type: '摄像头',
            useSprite: true,
            modelName: 'SXT_004'
        },
        {
            position: [-2.054, 3.760, 6.409],
            label: '摄像头4',
            color: '#ffffff',
            type: '摄像头',
            useSprite: true,
            modelName: 'SXT_003'
        },
        {
            position: [-4.352, 3.969, -5.490],
            label: '摄像头5',
            color: '#ffffff',
            type: '摄像头',
            useSprite: true,
            modelName: 'SXT_006'
        },
        {
            position: [-4.167, 2.807, -3.856],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '配电间1号空调',
            color: '#ffffff',
            type: '配电间空调',
            modelName: 'JFKT_002'
        },
        {
            position: [-4.288, 2.807, -5.705],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '配电间2号空调',
            color: '#ffffff',
            type: '配电间空调',
            modelName: 'JFKT_001'
        },
        // { position: [-4.23, 2.231, -1.563], label: '烟感', color: '#ffffff', type: '烟感' },
        // { position: [-2.996, 2.423, 1.793], label: '精密列头柜', color: '#ffffff', type: '精密列头柜' },
        { position: [2.423, 2.23, 0.452], rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2], label: '精密列头柜', color: '#ffffff', type: '精密列头柜' },
        // { position: [-1.282, 2.229, 0.031], label: '机房空调', color: '#ffffff', type: '机房空调' },
        // { position: [-2.045, 1.005, -4.063], label: '温湿度设备', color: '#ffffff', type: '温湿度设备' },
        {
            position: [-1.490, 2.509, 2.671],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '3号列间空调',
            color: '#ffffff',
            type: '3号列间空调',
            modelName: 'LJKT_003'
        },
        {
            position: [-3.324, 1.485, 2.339],
            rotation: [0.000, 1.571, 0.000],
            label: '后门指纹机',
            color: '#ffffff',
            type: '后门指纹机',
            modelName: 'MJ_002'
        },
        { 
            position: [0.844, 1.132, -4.794], 
            rotation: [-Math.PI / 180 * 94, 0, 
            Math.PI / 2], label: '2号UEUPS', 
            color: '#ffffff', 
            type: 'UPS',
            modelName: 'USP标签'
        },
        { 
            position: [0.197, 1.132, -4.771], 
            rotation: [-Math.PI / 180 * 94, 0, 
            Math.PI / 2], 
            label: '1号UEUPS', 
            color: '#ffffff', 
            type: 'UPS',
            modelName: 'USP标签'
        },
        {
            position: [1.284, 2.509, 2.790],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '1号列间空调',
            color: '#ffffff',
            type: '1号列间空调',
            modelName: 'LJKT_002'
        },
        {
            position: [0.763, 2.509, 0.553],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '2号列间空调',
            color: '#ffffff',
            type: '2号列间空调',
            modelName: 'LJKT_001'
        },
        {
            position: [0.668, 2.807, -6.046],
            rotation: [-Math.PI / 180 * 94, 0, Math.PI / 2],
            label: '配电柜',
            color: '#ffffff',
            type: '配电柜',
            modelName: 'PDG_001,PDG_002,PDG_003'
        },
        // {
        //     position: [-0.131, 2.224, -6.003],
        //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
        //     label: 'UPS输出柜',
        //     color: '#ffffff',
        //     type: 'UPS输出柜'
        // },
        // {
        //     position: [0.590, 2.224, -6.003],
        //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
        //     label: '市电输出柜',
        //     color: '#ffffff',
        //     type: '市电输出柜'
        // },
        // {
        //     position: [1.611, 2.224, -6.003],
        //     rotation: [Math.PI / 180, Math.PI / 180, -Math.PI / 180 * 0],
        //     label: '市电总切换柜',
        //     color: '#ffffff',
        //     type: '市电总切换柜'
        // },
    ];

    // 创建所有标签
    cabinetPoints.forEach((point, index) => {
        let label;

        // 根据是否使用精灵来创建不同类型的标签
        if (point.useSprite) {
            label = createSpriteLabel(point.label, point.color, point.type);
            // 精灵标签直接设置位置，不需要旋转
            label.position.set(point.position[0], point.position[1] + 0.35, point.position[2]);
        } else {
            label = createPlaneLabel(point.label, point.color, point.type);
            // 设置标签位置，轻微向上偏移避免与模型表面重叠
            label.position.set(point.position[0], point.position[1] + 0.35, point.position[2]);

            // 检查是否有自定义旋转角度
            if (point.rotation) {
                // 使用配置中的旋转角度
                const [rx, ry, rz] = point.rotation;
                label.rotation.set(rx, ry, rz);
            } else {
                // 使用默认的水平放置
                label.rotation.x = Math.PI / 2; // -90度，让平面水平放置
                label.rotation.y = 0; // 保持正对观察方向
                label.rotation.z = 0; // 不需要Z轴旋转
            }
        }

        // 合并userData信息，不要覆盖原来的type和deviceType
        label.userData = {
            ...label.userData,
            // type: 保持原来的 'plane-label' 或 'sprite-label'，不要覆盖
            // deviceType: 保持原来从 createPlaneLabel/createSpriteLabel 设置的值
            label: point.label,
            index: index,
            isClickable: true, // 标记为可点击对象
            originalX: point.position[0], // 保存原始X坐标
            originalY: point.position[1], // 保存原始Y坐标
            originalZ: point.position[2], // 保存原始Z坐标
            hasCustomRotation: !!point.rotation // 标记是否有自定义旋转
        };

        scene.add(label);
    });
}
// 识别机柜名称并返回对应的视角位置键
function getCabinetViewKey(objectName) {
    if (!objectName || typeof objectName !== 'string') {
        return null;
    }

    // 排除机柜内设备（如A0-SB001_2、B5-SB003_1等）
    const devicePattern = /^[AB]\d+-SB\d+(_\d+)?$/;
    if (devicePattern.test(objectName)) {
        return null;
    }

    // 首先检查是否有完全匹配的设备名称
    if (cabinetViewPositions[objectName]) {
        return objectName;
    }

    // 使用正则表达式匹配 A0-A9 或 B0-B9 开头的名称，支持带"柜"字的格式
    const cabinetPatterns = [
        /^([AB])([0-9])柜?$/,  // 匹配 A0, B0, A0柜, B0柜 等格式（精确匹配）
        /^([AB])([0-9])$/      // 原有的匹配模式作为备用（精确匹配）
    ];

    for (const pattern of cabinetPatterns) {
        const match = objectName.match(pattern);
        if (match) {
            const prefix = match[1]; // A 或 B
            const number = match[2]; // 0-9
            const key = prefix + number;

            // 检查是否存在对应的视角位置
            if (cabinetViewPositions[key]) {
                return key;
            }
        }
    }

    // 检查Box类型设备 (Box192_2, Box193_2 等)
    const boxMatch = objectName.match(/^(Box\d+_\d+)/);
    if (boxMatch && cabinetViewPositions[boxMatch[1]]) {
        return boxMatch[1];
    }

    // 检查机柜类型设备 (机柜4_1, 机柜008_1 等)
    const cabinetMatch = objectName.match(/^(机柜\d+_\d+)/);
    if (cabinetMatch && cabinetViewPositions[cabinetMatch[1]]) {
        return cabinetMatch[1];
    }

    // 模糊匹配 - 检查对象名称是否包含在预设位置的键中
    for (const key in cabinetViewPositions) {
        if (objectName.includes(key) || key.includes(objectName)) {
            return key;
        }
    }

    return null;
}
// 预设的机柜视角位置
const cabinetViewPositions = {
    'A0': {
        camera: { x: 2.527035, y: 1.689322, z: 1.172707 },
        target: { x: 2.343369, y: 1.265281, z: 3.708394 }
    },
    'A1': {
        camera: { x: 1.86965, y: 1.730524, z: 1.168938 },
        target: { x: 1.833986, y: 1.246823, z: 3.700343 }
    },
    'A2': {
        camera: { x: 1.265625, y: 1.751481, z: 1.164403 },
        target: { x: 1.229855, y: 1.268398, z: 3.695924 }
    },
    'A3': {
        camera: { x: 0.654083, y: 1.769479, z: 1.159196 },
        target: { x: 0.618314, y: 1.286396, z: 3.690718 }
    },
    'A4': {
        camera: { x: 0.063987, y: 1.780274, z: 1.152919 },
        target: { x: 0.028218, y: 1.297191, z: 3.68444 }
    },
    'A5': {
        camera: { x: -0.499859, y: 1.74057, z: 1.137375 },
        target: { x: -0.535628, y: 1.257487, z: 3.668896 }
    },
    'A6': {
        camera: { x: -1.103876, y: 1.729707, z: 1.126768 },
        target: { x: -1.139645, y: 1.246624, z: 3.658289 }
    },
    'A7': {
        camera: { x: -1.707944, y: 1.726247, z: 1.117572 },
        target: { x: -1.743713, y: 1.243164, z: 3.649094 }
    },
    'A8': {
        camera: { x: -2.305987, y: 1.73388, z: 1.110579 },
        target: { x: -2.341756, y: 1.250797, z: 3.6421 }
    },
    'A9': {
        camera: { x: -2.887556, y: 1.741554, z: 1.103768 },
        target: { x: -2.908548, y: 1.229046, z: 3.629663 }
    },
    'B0': {
        camera: { x: 2.431052, y: 1.751989, z: 2.3032 },
        target: { x: 2.435205, y: 1.364984, z: 0.637643 }
    },
    'B1': {
        camera: { x: 1.828284, y: 1.756427, z: 2.300666 },
        target: { x: 1.832437, y: 1.369422, z: 0.635109 }
    },
    'B2': {
        camera: { x: 1.235194, y: 1.76118, z: 2.298083 },
        target: { x: 1.239346, y: 1.374175, z: 0.632526 }
    },
    'B3': {
        camera: { x: 0.644658, y: 1.763375, z: 2.296074 },
        target: { x: 0.639297, y: 1.37637, z: 0.63052 }
    },
    'B4': {
        camera: { x: 0.042079, y: 1.758641, z: 2.299177 },
        target: { x: 0.036541, y: 1.371636, z: 0.633624 }
    },
    'B5': {
        camera: { x: -0.52646, y: 1.753908, z: 2.302167 },
        target: { x: -0.531997, y: 1.366903, z: 0.636614 }
    },
    'B6': {
        camera: { x: -1.114277, y: 1.756407, z: 2.303541 },
        target: { x: -1.119815, y: 1.369402, z: 0.637988 }
    },
    'B7': {
        camera: { x: -1.710293, y: 1.751541, z: 2.306548 },
        target: { x: -1.73457, y: 1.364536, z: 0.641162 }
    },
    'B8': {
        camera: { x: -2.300238, y: 1.746793, z: 2.316478 },
        target: { x: -2.325155, y: 1.359788, z: 0.651102 }
    },
    'B9': {
        camera: { x: -2.898133, y: 1.746808, z: 2.32542 },
        target: { x: -2.923049, y: 1.359803, z: 0.660043 }
    },
    // 新增设备视角位置
    'Box192_2': {
        camera: { x: -2.018322691791861, y: 1.9002220210487082, z: -3.419786387281972 },
        target: { x: -4.455223205560778, y: 1.6641730733620785, z: -3.383327102926209 }
    },
    'Box193_2': {
        camera: { x: -2.0536440509243152, y: 1.8758241709569983, z: -5.9385885711306186 },
        target: { x: -4.4905445646932325, y: 1.6397752232703686, z: -5.902129286774853 }
    },
    '机柜4_1': {
        camera: { x: 0.32732192559446294, y: 2.200807280243005, z: -4.677282399255573 },
        target: { x: 0.32227414914741237, y: -1.6766817100184142, z: -4.920186151943509 }
    },
    '机柜008_1': {
        camera: { x: 0.7018499256279381, y: 2.009832228813233, z: -4.819180891328949 },
        target: { x: 0.7013888252804366, y: -1.6809395128950255, z: -4.841369489639049 }
    },
    'Box196_2': {
        camera: { x: 1.4243044528237176, y: 2.070997279531184, z: -4.362999199419467 },
        target: { x: 1.583139655749082, y: 1.3736817208498964, z: -8.60798819241521 }
    },
    'Box195_2': {
        camera: { x: 0.6674050099426088, y: 2.0079109241385438, z: -4.382216599739208 },
        target: { x: 0.8020173572137339, y: 1.4096297547882721, z: -8.643125929810125 }
    },
    'Box194_2': {
        camera: { x: -0.10291236232423698, y: 2.0443422659724524, z: -4.411954187463886 },
        target: { x: -0.017654630891192503, y: 1.4460610966221807, z: -8.674136717852996 }
    }
};

// 收集符合特定命名规则的目标对象
function collectTargetObjects() {
    const targetObjects = [];

    if (!model) return targetObjects;

    model.traverse(child => {
        const name = child.name;

        // 检查是否符合目标对象的命名规则
        const isTargetObject =
            // A/B系列机柜 - 使用正则表达式匹配范围
            /^[AB]\d+$/.test(name) ||                    // A0-A9, B0-B9
            /^[AB]\d+-DOOR$/.test(name) ||               // A0-DOOR-A9-DOOR, B0-DOOR-B9-DOOR

            // 其他对象使用严格相等判断
            name === '机柜4' ||
            name === '机柜008' ||
            name === 'Box192' ||
            name === 'Box193' ||
            name === 'Box196' ||
            name === 'Box195' ||
            name === 'Box194' ||
            name === 'XXT_002' ||
            name === 'XXT_003' ||
            name === '玻璃门' ||
            name === '玻璃门1' ||
            name === '主玻璃门1' ||
            name === '主玻璃门2' ||
            name === 'wall' ||

            // 包含特定字符串的对象
            (name && name.includes('SXT')) ||            // 包含SXT的对象
            (name && name.includes('MJ')) ||             // 包含MJ的对象
            (name && name.includes('LJKT')) ||           // 包含LJKT的对象
            (name && name.includes('JFKT')) ||           // 包含JFKT的对象
            (name && name.includes('PDG')) ||
            (name && name.includes('UPS'))

        // 检查是否是选中机柜内的设备（如A0-SB001_2等）
        let isCabinetDevice = false;
        if (selectedCabinetKey.value && name) {
            const devicePattern = new RegExp(`^${selectedCabinetKey.value}-SB\\d+(_\\d+)?$`);
            isCabinetDevice = devicePattern.test(name);
            // if (isCabinetDevice) {
            //     console.log(`📦 collectTargetObjects发现机柜内设备: ${name}`);
            // }
        }

        if (isTargetObject || isCabinetDevice) {
            // 如果有选中的机柜，过滤掉选中机柜的主体对象（但保留门）
            if (selectedCabinetKey.value && isTargetObject) {
                const cabinetKey = getCabinetViewKey(name);
                // 如果是选中机柜的主体对象（不是门），则过滤掉
                if (cabinetKey === selectedCabinetKey.value && !name.toUpperCase().includes('DOOR')) {
                    // console.log(`🚫 过滤选中机柜的主体对象: ${name}`);
                    return;
                }
            }
            
            // 添加机柜内设备到目标对象中
            // if (isCabinetDevice) {
            //     console.log(`✅ 添加机柜内设备到交互对象: ${name}`);
            // }
            
            targetObjects.push(child);
        }
    });

    return targetObjects;
}

// 添加点击事件监听
function addClickListener() {
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();
    
    // 鼠标拖动检测变量
    let mouseDownPosition = { x: 0, y: 0 };
    let isMouseDown = false;
    let hasDragged = false;
    const dragThreshold = 5; // 像素阈值，超过这个距离就认为是拖动

    function onMouseDown(event) {
        isMouseDown = true;
        hasDragged = false;
        mouseDownPosition.x = event.clientX;
        mouseDownPosition.y = event.clientY;
    }

    function onMouseMove(event) {
        if (isMouseDown) {
            const deltaX = Math.abs(event.clientX - mouseDownPosition.x);
            const deltaY = Math.abs(event.clientY - mouseDownPosition.y);
            
            // 如果鼠标移动距离超过阈值，标记为拖动
            if (deltaX > dragThreshold || deltaY > dragThreshold) {
                hasDragged = true;
            }
        }
    }

    function onMouseUp(event) {
        isMouseDown = false;
    }

    function onMouseClick(event) {
        // 如果发生了拖动，取消点击事件
        if (hasDragged) {
            console.log('🚫 检测到拖动，取消点击事件');
            return;
        }
        // 计算鼠标位置
        const container = document.getElementById('map');
        const rect = container.getBoundingClientRect();

        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 设置射线
        raycaster.setFromCamera(mouse, camera);

        // // 首先检测标签的点击（包括平面标签和精灵标签）
        // // 收集所有可点击且可见的标签
        // const clickableLabels = [];
        // scene.traverse(child => {
        //     if (((child.type === 'Mesh' && child.userData.type === 'plane-label') ||
        //         (child.type === 'Sprite' && child.userData.type === 'sprite-label')) &&
        //         child.userData.isClickable && child.visible) {
        //         clickableLabels.push(child);
        //     }
        // });

        // const labelIntersects = raycaster.intersectObjects(clickableLabels);

        // if (labelIntersects.length > 0) {
        //     // 点击了标签（平面标签或精灵标签）
        //     const clickedLabel = labelIntersects[0].object;
        //     handleMarkerClick(clickedLabel.userData);
        //     return; // 处理完标签点击后直接返回，不继续检测模型
        // }

        // 如果没有点击精灵标签，则检测模型
        if (model) {
            // 收集特定名称的对象到数组中
            const targetObjects = collectTargetObjects();

            // 使用收集到的目标对象进行射线检测
            const modelIntersects = raycaster.intersectObjects(targetObjects, true);

            if (modelIntersects.length > 0) {
                const intersection = modelIntersects[0];
                const clickedObject = intersection.object;
                const clickPoint = intersection.point;
                const clickFace = intersection.face;

                console.log('🎯 点击了模型:', {
                    对象名称: clickedObject.name || '未命名对象',
                    点击位置: {
                        x: clickPoint.x.toFixed(3),
                        y: clickPoint.y.toFixed(3),
                        z: clickPoint.z.toFixed(3)
                    },
                    距离: intersection.distance.toFixed(3),
                    面索引: clickFace ? clickFace.a + ',' + clickFace.b + ',' + clickFace.c : '无',
                    UV坐标: intersection.uv ? {
                        u: intersection.uv.x.toFixed(3),
                        v: intersection.uv.y.toFixed(3)
                    } : '无',
                    clickedObject: clickedObject
                });

                                 // 检查名称中是否包括SXT、MJ、LJKT、JFKT、PDG、UPS
                 if (clickedObject.name && (clickedObject.name.includes('SXT') || clickedObject.name.includes('MJ') || clickedObject.name.includes('LJKT') || clickedObject.name.includes('JFKT') || clickedObject.name.includes('PDG') || clickedObject.name.includes('UPS'))) {
                     console.log('🔍 点击了标签:', clickedObject.name);
                     let modelName = clickedObject.name;
                     for (let i = 0; i < points.length; i++) {
                         // 检查 modelName 是否存在且不为空
                         if (points[i].modelName && typeof points[i].modelName === 'string') {
                             // 处理逗号分割的 modelName
                             const modelNames = points[i].modelName.split(',').map(name => name.trim());
                             // 检查是否有任何一个分割后的名称与点击的对象名称匹配
                             if (modelNames.includes(modelName)) {
                                 console.log('✅ 找到匹配的点位:', points[i]);
                                 console.log('🔍 匹配的modelNames:', modelNames);
                                 handleMarkerClick(points[i]);
                                 break;
                             }
                         }
                     }
                     return;
                 }

                // 检查是否是主玻璃门
                if (clickedObject.name && clickedObject.name.includes('主玻璃门')) {
                    let obj1 = model.getObjectByName('主玻璃门2_1');
                    handleMainGlassDoor(obj1);
                    let obj2 = model.getObjectByName('主玻璃门1_1');
                    handleMainGlassDoor(obj2);
                }
                // 检查是否是其他玻璃门
                else if (clickedObject.name && (clickedObject.name.includes('玻璃门_1') || clickedObject.name.includes('玻璃门1_1'))) {
                    let obj1 = model.getObjectByName('玻璃门1_1');
                    handleGlassDoor(obj1);
                    let obj2 = model.getObjectByName('玻璃门_1');
                    handleGlassDoor(obj2);
                }
                // 检查是否是机柜门（包含DOOR关键词）
                else if (clickedObject.name && clickedObject.name.toUpperCase().includes('DOOR')) {
                    let cabinetKey = getCabinetViewKey(clickedObject.name);
                    
                    // 判断是否是当前选中机柜的门
                    if (selectedCabinetKey.value && cabinetKey === selectedCabinetKey.value) {
                        // 是当前选中机柜的门，关门并取消选中
                        console.log(`🔓 点击选中机柜的门，关闭门: ${cabinetKey}`);
                        handleCabinetDoor(clickedObject, false); // 强制关闭门
                        selectedCabinetKey.value = null; // 取消选中状态
                    } else {
                        // 不是当前选中机柜的门，正常处理
                        console.log(`🔐 点击其他机柜的门: ${cabinetKey}`);
                        handleCabinetDoor(clickedObject);
                        handleClickJiGuang(cabinetKey, clickedObject);
                    }
                }
                else {
                    console.log(`🎯 点击了对象: ${clickedObject.name}`)
                    
                    // 优先检查是否是机柜内设备（如A0-SB001_2等）
                    let isCabinetDevice = false;
                    if (selectedCabinetKey.value && clickedObject.name) {
                        const devicePattern = new RegExp(`^${selectedCabinetKey.value}-SB\\d+(_\\d+)?$`);
                        isCabinetDevice = devicePattern.test(clickedObject.name);
                        console.log(`🔍 检查是否是机柜内设备: ${clickedObject.name}, 选中机柜: ${selectedCabinetKey.value}, 是否匹配: ${isCabinetDevice}`);
                    }
                    
                    if (isCabinetDevice) {
                        // 处理机柜内设备点击
                        console.log(`🔧 确认是机柜内设备，调用handleCabinetDeviceClick: ${clickedObject.name}`);
                        handleCabinetDeviceClick(clickedObject);
                    } else {
                        // 处理其他可识别的设备（用于视角切换）
                        let cabinetKey = getCabinetViewKey(clickedObject.name);
                        console.log(`🏢 检查是否是机柜: ${clickedObject.name}, 获得机柜key: ${cabinetKey}`);
                        let targetObject = clickedObject;
                        handleClickJiGuang(cabinetKey, targetObject);
                    }
                }
            }
        }
    }

    // 添加事件监听器
    const container = document.getElementById('map');
    container.addEventListener('mousedown', onMouseDown);
    container.addEventListener('mousemove', onMouseMove);
    container.addEventListener('mouseup', onMouseUp);
    container.addEventListener('click', onMouseClick);
    
    // 返回清理函数
    return function cleanup() {
        container.removeEventListener('mousedown', onMouseDown);
        container.removeEventListener('mousemove', onMouseMove);
        container.removeEventListener('mouseup', onMouseUp);
        container.removeEventListener('click', onMouseClick);
    };
}

function handleClickJiGuang(cabinetKey, clickedObject) {
    // 如果通过对象名称没有找到，尝试通过parent查找
    if (!cabinetKey) {
        const cabinetParent = findCabinetParent(clickedObject);
        if (cabinetParent && cabinetParent !== clickedObject) {
            cabinetKey = getCabinetViewKey(cabinetParent.name);
            targetObject = cabinetParent;
        }
    }

    if (cabinetKey) {
        // 检查是否点击了已选中的机柜
        if (selectedCabinetKey.value === cabinetKey) {
            console.log(`🔓 取消选中机柜: ${cabinetKey}`);
            
            // 收回该机柜中的所有抽出设备
            retractAllDevicesInCabinet(cabinetKey);
            
            // 取消选中状态
            selectedCabinetKey.value = null;
            
            // 关闭对应的机柜门
            let doorName = cabinetKey + '-DOOR_4';
            let doorObject = null;
            model.traverse((child) => {
                if (child.name === doorName) {
                    doorObject = child;
                }
            });
            
            if (doorObject) {
                handleCabinetDoor(doorObject, false); // 强制关闭门
                console.log(`✅ 已关闭机柜门: ${doorName}`);
            }
            
            return;
        }
        
        // 如果之前有选中的机柜，先收回设备并关闭它的门
        if (selectedCabinetKey.value) {
            console.log(`🔒 关闭之前选中的机柜: ${selectedCabinetKey.value}`);
            
            // 收回之前机柜中的所有抽出设备
            retractAllDevicesInCabinet(selectedCabinetKey.value);
            
            // 关闭之前机柜的门
            let prevDoorName = selectedCabinetKey.value + '-DOOR_4';
            let prevDoorObject = null;
            model.traverse((child) => {
                if (child.name === prevDoorName) {
                    prevDoorObject = child;
                }
            });
            
            if (prevDoorObject) {
                handleCabinetDoor(prevDoorObject, false); // 强制关闭门
            }
        }
        
        // 选中新的机柜
        selectedCabinetKey.value = cabinetKey;
        console.log(`🔐 选中机柜: ${cabinetKey}，现在可以选择机柜内部组件`);

        // 获取对应设备的预设视角位置
        const targetView = cabinetViewPositions[cabinetKey];
        let doorName = cabinetKey + '-DOOR_4';
        if (targetView) {
            // 使用平滑动画移动相机到预设视角
            smoothMoveCamera(targetView, 1500)
        }
        
        // 查找并打开对应的机柜门
        let doorObject = null;
        model.traverse((child) => {
            if (child.name === doorName) {
                doorObject = child;
            }
        });
        
        
        if (doorObject) {
            // 调用现有的机柜门处理函数来强制打开门
            handleCabinetDoor(doorObject, true);
            console.log(`✅ 已打开机柜门: ${doorName}`);
        } else {
            console.warn(`未找到机柜门: ${doorName}`);
        }
    }
}

// 处理机柜内设备点击
function handleCabinetDeviceClick(clickedObject) {
    console.log(`🔧 handleCabinetDeviceClick被调用! 设备名称: ${clickedObject.name}`);
    console.log(`📋 当前选中的机柜: ${selectedCabinetKey.value}`);
    
    // 找到设备的整体对象（可能是父对象或group）
    const deviceToMove = findDeviceToMove(clickedObject);
    
    // 保存原始位置（如果还没有保存的话）
    if (deviceToMove.userData.originalPosition === undefined) {
        deviceToMove.userData.originalPosition = deviceToMove.position.clone();
        console.log(`💾 保存设备原始位置: ${deviceToMove.name || clickedObject.name}, z: ${deviceToMove.position.z}`);
    }
    
    // 检查设备是否已经被抽出（从设备整体对象获取状态）
    const isExtended = deviceToMove.userData.isExtended || false;
    
    // 根据机柜名称决定移动方向
    const cabinetName = selectedCabinetKey.value;
    let targetZOffset = 0;
    
    if (cabinetName && cabinetName.startsWith('B')) {
        // A开头的机柜，设备向前抽出
        targetZOffset = isExtended ? 0 : 0.3;
        console.log(`📈 A系列机柜设备${isExtended ? '收回' : '抽出'}，z轴${isExtended ? '恢复' : '增加0.3'}`);
    } else if (cabinetName && cabinetName.startsWith('A')) {
        // B开头的机柜，设备向后抽出
        targetZOffset = isExtended ? 0 : -0.3;
        console.log(`📉 B系列机柜设备${isExtended ? '收回' : '抽出'}，z轴${isExtended ? '恢复' : '减少0.3'}`);
    } else {
        console.warn(`❓ 未知的机柜类型: ${cabinetName}`);
        return;
    }
    
    // 计算目标位置
    const originalPosition = deviceToMove.userData.originalPosition;
    const targetZ = originalPosition.z + targetZOffset;
    
    // 如果要抽出设备，先收回当前机柜中的其他已抽出设备，等待完成后再执行新的移动
    if (!isExtended) {
        console.log(`🔄 抽出新设备前，先收回其他设备`);
        // 抽出设备时清除悬浮高亮
        clearCabinetHighlight();
        console.log(`✨ 清除悬浮高亮效果 - 设备抽出`);
        
        // 移除复杂的视角调整逻辑，使用新的设备视角移动功能
        
        retractAllDevicesInCabinet(selectedCabinetKey.value, deviceToMove).then(() => {
            // 其他设备收回完成后，执行新设备的抽出动画
            animateDeviceMovement(deviceToMove, targetZ, '抽出').then(() => {
                // 更新状态到设备整体对象上
                deviceToMove.userData.isExtended = true;
                
                // 设备抽出完成后，移动相机到设备面前
                console.log(`🎬 设备抽出完成，开始移动相机到设备面前: ${deviceToMove.name}`);
                moveToDeviceView(deviceToMove, cabinetName, 1200).then(() => {
                    console.log(`✅ 相机已移动到设备 ${deviceToMove.name} 面前`);
                }).catch((error) => {
                    console.error(`❌ 移动相机到设备面前失败:`, error);
                });
            });
        });
    } else {
        // 收回设备，直接执行动画
        console.log(`🔄 收回设备`);
        // 收回设备时清除悬浮高亮
        clearCabinetHighlight();
        console.log(`✨ 清除悬浮高亮效果 - 设备收回`);
        
        animateDeviceMovement(deviceToMove, targetZ, '收回').then(() => {
            // 更新状态到设备整体对象上
            deviceToMove.userData.isExtended = false;
            
            // 设备收回完成后，相机返回到机柜视角
            console.log(`🎬 设备收回完成，相机返回机柜视角: ${cabinetName}`);
            const cabinetView = cabinetViewPositions[cabinetName];
            if (cabinetView) {
                smoothMoveCamera(cabinetView, 1000).then(() => {
                    console.log(`✅ 相机已返回机柜 ${cabinetName} 视角`);
                }).catch((error) => {
                    console.error(`❌ 返回机柜视角失败:`, error);
                });
            }
        });
    }
}

// 收回指定机柜中的所有抽出设备
function retractAllDevicesInCabinet(cabinetKey, excludeDevice = null) {
    if (!cabinetKey || !model) {
        return Promise.resolve();
    }
    
    console.log(`📦 开始收回机柜 ${cabinetKey} 中的所有抽出设备`);
    
    const retractPromises = [];
    
    // 遍历模型中的所有对象，查找该机柜的设备
    model.traverse((child) => {
        if (child.name && child.name.includes && child.name.includes('-SB')) {
            // 检查是否是指定机柜的设备
            const devicePattern = new RegExp(`^${cabinetKey}-SB\\d+(_\\d+)?`);
            if (devicePattern.test(child.name)) {
                // 找到设备的整体对象
                const deviceToMove = findDeviceToMove(child);
                
                // 跳过排除的设备
                if (excludeDevice && deviceToMove === excludeDevice) {
                    return;
                }
                
                // 检查设备是否已抽出
                const isExtended = deviceToMove.userData.isExtended || false;
                if (isExtended && deviceToMove.userData.originalPosition) {
                    console.log(`🔄 收回设备: ${child.name}`);
                    
                    // 计算原始位置
                    const originalPosition = deviceToMove.userData.originalPosition;
                    const targetZ = originalPosition.z;
                    
                    // 执行动画收回并收集Promise
                    const retractPromise = animateDeviceMovement(deviceToMove, targetZ, '收回');
                    retractPromises.push(retractPromise);
                    
                    // 更新状态
                    deviceToMove.userData.isExtended = false;
                }
            }
        }
    });
    
    // 等待所有设备收回完成
    return Promise.all(retractPromises);
}

// 找到需要移动的设备整体对象
function findDeviceToMove(clickedObject) {
    // 首先尝试向上查找，看是否有包含完整设备的父对象
    let current = clickedObject;
    
    // 检查当前对象的parent，看是否是设备的group或container
    while (current.parent) {
        const parent = current.parent;
        
        // 如果parent的名称也包含设备相关信息，说明可能是设备的容器
        if (parent.name && (
            parent.name.includes('-SB') || 
            parent.type === 'Group'
        )) {
            console.log(`🔍 找到设备容器: ${parent.name || parent.type}, 使用此对象进行移动`);
            return parent;
        }
        
        // 如果parent是模型的根对象，停止查找
        if (parent === model) {
            break;
        }
        
        current = parent;
    }
    
    // 如果没有找到合适的父对象，使用点击的对象本身
    console.log(`🎯 未找到设备容器，直接移动点击对象: ${clickedObject.name}`);
    return clickedObject;
}

// 设备移动动画函数
function animateDeviceMovement(deviceObject, targetZ, action) {
    const startZ = deviceObject.position.z;
    const startTime = performance.now();
    const duration = 800; // 动画持续时间
    
    console.log(`🎬 开始${action}动画: ${deviceObject.name || 'unnamed'}, 从 z:${startZ.toFixed(3)} 到 z:${targetZ.toFixed(3)}`);
    
    return new Promise((resolve) => {
        function animate(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数实现平滑动画
            const easeProgress = easeInOutCubic(progress);
            
            // 插值计算当前Z位置
            const currentZ = startZ + (targetZ - startZ) * easeProgress;
            deviceObject.position.z = currentZ;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                console.log(`✅ ${action}动画完成: ${deviceObject.name || 'unnamed'}, 最终位置 z:${deviceObject.position.z.toFixed(3)}`);
                resolve(); // 动画完成时resolve Promise
            }
        }
        
        requestAnimationFrame(animate);
    });
}

// 全局高亮相关变量
let currentHighlightedCabinet = null;
let highlightBoxes = [];

// 清除机柜高亮
function clearCabinetHighlight() {
    highlightBoxes.forEach(box => {
        scene.remove(box);
        if (box.geometry) box.geometry.dispose();
        if (box.material) box.material.dispose();
    });
    highlightBoxes = [];
    currentHighlightedCabinet = null;
}

// 高亮机柜
function highlightCabinet(cabinetObject) {
    if (!cabinetObject) return;
    // 计算对象的边界盒
    const box = new THREE.Box3().setFromObject(cabinetObject);

    // 检查边界盒是否有效
    if (box.isEmpty()) {
        console.warn('边界盒为空，跳过高亮');
        return;
    }

    const size = box.getSize(new THREE.Vector3());
    const center = box.getCenter(new THREE.Vector3());
    const boxGeometry = new THREE.BoxGeometry(size.x, size.y, size.z);
    const edges = new THREE.EdgesGeometry(boxGeometry);
    
    // 创建主线框
    const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x00ffff,
        linewidth: 3,
        transparent: false,
        opacity: 1.0,
        depthTest: false,
        blending: THREE.AdditiveBlending,
    });

    const wireframe = new THREE.LineSegments(edges, lineMaterial);
    wireframe.position.copy(center);
    scene.add(wireframe);
    highlightBoxes.push(wireframe);

    // 添加多层发光效果确保所有面都发光
    // 第一层发光 - 内层
    const glowGeometry1 = new THREE.BoxGeometry(
        size.x * 1.05,
        size.y * 1.05,
        size.z * 1.05,
    );

    const glowMaterial1 = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.1,
        side: THREE.BackSide,
        blending: THREE.AdditiveBlending,
        depthTest: false,
        transparent: true,
    });

    const glowMesh1 = new THREE.Mesh(glowGeometry1, glowMaterial1);
    glowMesh1.position.copy(center);
    scene.add(glowMesh1);
    highlightBoxes.push(glowMesh1);
}

// 添加鼠标悬停事件监听
function addHoverListener() {
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    function onMouseMove(event) {
        // 计算鼠标位置
        const hoverContainer = document.getElementById('map');
        const rect = hoverContainer.getBoundingClientRect();

        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 设置射线
        raycaster.setFromCamera(mouse, camera);

        // 检测模型 - 使用和点击事件相同的目标对象
        if (model) {
            // 收集特定名称的对象到数组中
            const targetObjects = collectTargetObjects();
            const modelIntersects = raycaster.intersectObjects(targetObjects, true);

            if (modelIntersects.length > 0) {
                const intersection = modelIntersects[0];
                console.log(intersection);
                
                const hoveredObject = intersection.object;

                // 检查是否是机柜
                if (hoveredObject.name && isCabinetObject(hoveredObject.name)) {
                    // 找到整个机柜对象
                    const cabinetObject = findCabinetParentForHover(hoveredObject);
                    const parentToHighlight = cabinetObject && cabinetObject.parent ? cabinetObject.parent : cabinetObject;
                    // 高亮机柜（首次或切换）
                    if (parentToHighlight !== currentHighlightedCabinet) {
                        clearCabinetHighlight();
                        highlightCabinet(parentToHighlight);
                        currentHighlightedCabinet = parentToHighlight;
                    }
                    // 始终显示并更新tooltip，使用全局鼠标坐标
                    if (parentToHighlight.name) {
                        const mappedName = mapDeviceName(parentToHighlight.name);
                        tooltip.value.show = true;
                        tooltip.value.content = mappedName;
                        
                        // 使用全局追踪的鼠标坐标定位tooltip
                        const tooltipEl = document.querySelector('.hover-tooltip');
                        let x = globalMousePosition.value.x + 10; // 添加10px偏移，避免遮挡鼠标
                        let y = globalMousePosition.value.y + 10;
                        // 边界检查，确保tooltip不超出屏幕范围
                        if (tooltipEl) {
                            const w = tooltipEl.offsetWidth;
                            const h = tooltipEl.offsetHeight;
                            const m = 10; // 边距
                            
                            // 检查右边界
                            if (x + w > window.innerWidth - m) {
                                x = globalMousePosition.value.x - w - 10; // 显示在鼠标左侧
                            }
                            
                            // 检查下边界
                            if (y + h > window.innerHeight - m) {
                                y = globalMousePosition.value.y - h - 10; // 显示在鼠标上方
                            }
                            
                            // 确保不会超出左上边界
                            x = Math.max(m, x);
                            y = Math.max(m, y);
                        }
                        
                        tooltip.value.x = x;
                        tooltip.value.y = y;
                        console.log('💡 Tooltip位置更新 - 鼠标坐标:', globalMousePosition.value.x, globalMousePosition.value.y, '最终tooltip位置:', x, y);
                    }
                } else {
                    // 如果没有悬停在机柜上，清除高亮和tooltip
                    clearCabinetHighlight();
                    tooltip.value.show = false;
                }
            } else {
                // 没有击中任何对象，清除高亮和tooltip
                clearCabinetHighlight();
                tooltip.value.show = false;
            }
        }
    }

    // 判断是否是机柜对象或机柜内设备
    function isCabinetObject(objectName) {
        if (!objectName) return false;

        // 检查是否是机柜内设备（如A0-SB001_2等）
        if (selectedCabinetKey.value) {
            const devicePattern = new RegExp(`^${selectedCabinetKey.value}-SB\\d+(_\\d+)?$`);
            if (devicePattern.test(objectName)) {
                return true;
            }
        }

        // 检查是否包含机柜相关的关键词
        const cabinetKeywords = ['IT柜', '机柜', 'cabinet', 'rack', 'jigui'];
        const keywordMatch = cabinetKeywords.some(keyword => objectName.toLowerCase().includes(keyword.toLowerCase()));

        // 检查各种设备命名格式
        const patternMatches = [
            /^[AB]\d+/.test(objectName),           // 匹配A0-A9, B0-B9格式
            /^[AB]\d+柜/.test(objectName),         // 匹配A0柜, B0柜格式
            /IT柜_\d+/.test(objectName),           // 匹配IT柜_001格式
            /机柜\d+/.test(objectName),            // 匹配机柜001格式
            /^Box\d+_\d+/.test(objectName),        // 匹配Box192_2等格式
            /^机柜\d+_\d+/.test(objectName)        // 匹配机柜4_1, 机柜008_1等格式
        ];

        // 检查是否在预设的视角位置中（这意味着它是可点击的设备）
        const isInViewPositions = cabinetViewPositions.hasOwnProperty(objectName);

        return keywordMatch || patternMatches.some(match => match) || isInViewPositions;
    }

    // 找到机柜的父对象（用于悬停效果）
    function findCabinetParentForHover(object) {
        let current = object;

        // 向上遍历，找到包含完整设备的对象
        while (current) {
            // 检查当前对象的name
            if (current.name && isCabinetObject(current.name)) {
                return current;
            }

            // 检查parent对象及其group属性
            if (current.parent) {
                // 检查parent的name
                if (current.parent.name && isCabinetObject(current.parent.name)) {
                    return current.parent;
                }

                // 检查parent的group属性
                if (current.parent.userData && current.parent.userData.group && isCabinetObject(current.parent.userData.group)) {
                    return current.parent;
                }

                // 检查parent对象本身的group属性（如果直接存在）
                if (current.parent.group && isCabinetObject(current.parent.group)) {
                    return current.parent;
                }

                // 特殊处理：检查parent的name是否包含我们关注的设备名称
                const parentName = current.parent.name;
                if (parentName) {
                    for (const deviceKey in cabinetViewPositions) {
                        if (parentName.includes(deviceKey) || deviceKey.includes(parentName)) {
                            return current.parent;
                        }
                    }
                }
            }

            current = current.parent;
        }
        return object; // 如果没找到合适的父对象，返回原对象
    }



    // 鼠标离开容器时隐藏tooltip
    function onMouseLeave() {
        tooltip.value.show = false;
        clearCabinetHighlight();
        console.log('🚫 鼠标离开容器，隐藏tooltip');
    }

    // 添加事件监听器
    const hoverEventContainer = document.getElementById('map');
    hoverEventContainer.addEventListener('mousemove', onMouseMove);
    hoverEventContainer.addEventListener('mouseleave', onMouseLeave);

    // 清理函数
    const cleanup = () => {
        hoverEventContainer.removeEventListener('mousemove', onMouseMove);
        hoverEventContainer.removeEventListener('mouseleave', onMouseLeave);
        clearCabinetHighlight();
        tooltip.value.show = false;
    };

    // 将清理函数暴露到全局
    if (window.threeDebug) {
        window.threeDebug.clearHoverEffects = cleanup;
    }

    return cleanup;
}

// 处理标记点击事件
function handleMarkerClick(markerData) {
    currentClickMarkerInfo.value = markerData;

    // 使用deviceType来判断设备类型
    const deviceType = markerData.deviceType || markerData.type;

    if (deviceType === '门禁' || deviceType === '指纹' || deviceType === '前门指纹机' || deviceType === '后门指纹机') {
        showMenjinList.value = true;
    } else if (deviceType === '摄像头') {
        // 处理摄像头点击 - 显示视频
        const cameraName = markerData.label || markerData.text;
        showVideoModal.value = true;
        
        // 初始化视频播放器
        initVideoPlayer(cameraName);
    } else {
        // 点击设备详情前，清除机柜高亮
        clearCabinetHighlight();
        showDeviceDetail.value = true;

        // 根据设备名称从接口数据中查找对应的设备信息
        const deviceName = markerData.label || markerData.text;
        const deviceData = findDeviceByName(deviceName);

        if (deviceData) {
            deviceDetail.value = deviceData;
        } else {
            // 如果没有找到对应的设备数据，显示暂无数据
            deviceDetail.value = {
                deviceName: deviceName,
                canshu: []
            };
        }
    }
}

// 根据设备名称查找设备数据
function findDeviceByName(deviceName) {
    if (deviceListData.value.length === 0) {
        return null;
    }

    // 直接根据 deviceName 匹配
    let device = deviceListData.value.find(item => item.deviceName === deviceName);

    if (device) {
        return device;
    }

    // 如果没有找到完全匹配的，尝试模糊匹配
    // 处理一些可能的命名差异
    const deviceNameMappings = {
        '前门指纹机': '前门指纹机',
        '后门指纹机': '后门指纹机',
        '精密列头柜': '精密列头柜',
        '列间空调': ['1号列间空调', '2号列间空调', '3号列间空调'],
        '配电间1号空调': '配电间1号空调',
        '配电间2号空调': '配电间2号空调',
        '1号UEUPS': '1号UEUPS',
        '2号UEUPS': '2号UEUPS',
        '配电柜': ['市电总切换柜', '市电输出柜', 'UPS输出柜'],
        '机房环境': '机房环境'
    };

    // 尝试通过映射查找
    const mappedNames = deviceNameMappings[deviceName];
    if (mappedNames) {
        if (Array.isArray(mappedNames)) {
            // 如果是数组，返回第一个找到的
            for (const name of mappedNames) {
                device = deviceListData.value.find(item => item.deviceName === name);
                if (device) {
                    return device;
                }
            }
        } else {
            device = deviceListData.value.find(item => item.deviceName === mappedNames);
            if (device) {
                return device;
            }
        }
    }

    // 最后尝试包含匹配
    console.log('🔄 尝试包含匹配');
    device = deviceListData.value.find(item =>
        item.deviceName.includes(deviceName) || deviceName.includes(item.deviceName)
    );

    if (device) {
        console.log(`✅ 通过包含匹配找到设备:`, device);
        return device;
    }

    console.warn(`❌ 未找到设备: "${deviceName}"`);
    return null;
}

// 重置到初始视角
function resetToInitialView() {
    if (!camera || !controls) {
        console.warn('相机或控制器未初始化，无法重置视角');
        return;
    }

    // 定义初始视角位置（与initThreeScene中的初始位置一致）
    const initialView = {
        camera: {
            x: 3.890274067613357,
            y: 9.352182239442273,
            z: -0.08217628147574355
        },
        target: {
            x: -0.6548553429473142,
            y: 0.11413210465790953,
            z: -0.06706507811251107
        }
    };

    // 关闭所有门（包括主玻璃门和机柜门）
    closeAllDoors();

    // 使用平滑动画移动到初始视角
    smoothMoveCamera(initialView, 2000)
}

// 添加多机柜温度效果
function addHighTemperatureEffect() {
    if (!model) return;

    // 定义机柜温度数据（模拟数据）
    const cabinetTemperatures = [
        { name: 'IT柜_001', temperature: 50, position: null },
        { name: 'IT柜_015', temperature: 50, position: null },
    ];

    // 存储所有应用了温度效果的机柜
    const temperatureEffects = [];

    // 查找所有机柜并应用温度效果
    model.traverse((child) => {
        if (child.name && child.name.includes('IT柜_')) {
            const cabinetData = cabinetTemperatures.find(item => item.name === child.name);
            if (cabinetData) {
                cabinetData.position = child.position.clone();

                // 根据温度范围确定颜色和强度
                const tempEffect = getTemperatureEffect(cabinetData.temperature);

                // 创建发光材质
                const glowMaterial = new THREE.MeshStandardMaterial({
                    color: tempEffect.color,
                    emissive: tempEffect.emissive,
                    emissiveIntensity: tempEffect.emissiveIntensity,
                    transparent: true,
                    opacity: 0.8
                });

                // 应用发光材质
                if (child.isMesh) {
                    child.userData.originalMaterial = child.material;
                    child.material = glowMaterial;
                } else {
                    child.traverse((meshChild) => {
                        if (meshChild.isMesh) {
                            meshChild.userData.originalMaterial = meshChild.material;
                            meshChild.material = glowMaterial;
                        }
                    });
                }

                // 添加点光源
                const pointLight = new THREE.PointLight(tempEffect.lightColor, tempEffect.lightIntensity, 10);
                pointLight.position.copy(child.position);
                pointLight.position.y += 2;
                scene.add(pointLight);


                // 存储效果信息
                temperatureEffects.push({
                    cabinet: child,
                    light: pointLight,
                    material: glowMaterial,
                    temperature: cabinetData.temperature
                });
            }
        }
    });

    // 将相关对象存储到全局调试对象中
    if (window.threeDebug) {
        window.threeDebug.temperatureEffects = temperatureEffects;
        window.threeDebug.removeHighTempEffect = () => {
            temperatureEffects.forEach(effect => {
                // 恢复原始材质
                if (effect.cabinet.isMesh) {
                    if (effect.cabinet.userData.originalMaterial) {
                        effect.cabinet.material = effect.cabinet.userData.originalMaterial;
                    }
                } else {
                    effect.cabinet.traverse((child) => {
                        if (child.isMesh && child.userData.originalMaterial) {
                            child.material = child.userData.originalMaterial;
                        }
                    });
                }

                // 移除点光源
                scene.remove(effect.light);

                // 清理资源
                if (effect.material) effect.material.dispose();
            });

            // 清空数组
            temperatureEffects.length = 0;
        };

        // 更新温度数据的方法
        window.threeDebug.updateTemperatures = (newTemperatures) => {
            // 先移除现有效果
            window.threeDebug.removeHighTempEffect();
            // 更新温度数据
            if (newTemperatures && Array.isArray(newTemperatures)) {
                cabinetTemperatures.splice(0, cabinetTemperatures.length, ...newTemperatures);
            }
            // 重新添加效果
            addHighTemperatureEffect();
        };
    }
}

// 根据温度获取效果配置
function getTemperatureEffect(temperature) {
    if (temperature >= 50) {
        // 高温：红色
        return {
            color: 0xff3333,
            emissive: 0xff1111,
            emissiveIntensity: 0.8,
            lightColor: 0xff3333,
            lightIntensity: 2.0,
        };
    } else if (temperature >= 40) {
        // 中高温：橙色
        return {
            color: 0xff8833,
            emissive: 0xff4411,
            emissiveIntensity: 0.6,
            lightColor: 0xff8833,
            lightIntensity: 1.5,
        };
    } else if (temperature >= 30) {
        // 正常温度：绿色
        return {
            color: 0x33ff33,
            emissive: 0x11ff11,
            emissiveIntensity: 0.4,
            lightColor: 0x33ff33,
            lightIntensity: 1.0,
        };
    } else {
        // 低温：蓝色
        return {
            color: 0x3333ff,
            emissive: 0x1111ff,
            emissiveIntensity: 0.3,
            lightColor: 0x3333ff,
            lightIntensity: 0.8,
        };
    }
}

// 关闭所有其他已打开的门
function closeAllOtherDoors(currentDoorObject) {
    if (!model) return;

    let closedDoorsCount = 0;

    // 遍历模型中的所有对象，查找门对象
    model.traverse((child) => {
        // 跳过当前点击的门
        if (child === currentDoorObject) {
            return;
        }

        // 检查是否是门对象（包含DOOR关键词、主玻璃门或其他玻璃门）
        const isDoor = child.name && child.name.toUpperCase().includes('DOOR');
        const isMainGlassDoor = child.name && child.name.includes('主玻璃门');
        const isOtherGlassDoor = child.name && (child.name.includes('玻璃门_1') || child.name.includes('玻璃门1_1'));

        if (isDoor || isMainGlassDoor || isOtherGlassDoor) {
            const doorParent = child.parent;
            if (doorParent && doorParent.userData.originalRotationZ !== undefined) {
                // 检查门的isOpen状态
                const isOpen = doorParent.userData.isOpen || false;

                if (isOpen) {
                    // 门已打开，先收回设备再关闭门
                    const originalRotation = doorParent.userData.originalRotationZ;
                    
                    // 如果是机柜门，先收回对应机柜的设备
                    if (isDoor && child.name.toUpperCase().includes('DOOR')) {
                        const cabinetKey = getCabinetViewKey(child.name);
                        if (cabinetKey) {
                            console.log(`🔄 关闭其他机柜门，先收回机柜 ${cabinetKey} 中的所有设备`);
                            retractAllDevicesInCabinet(cabinetKey).then(() => {
                                // 设备收回完成后，再关闭门
                                animateRotation(doorParent, 'z', doorParent.rotation.z, originalRotation, 600);
                                doorParent.userData.isOpen = false;
                            });
                        } else {
                            // 不是机柜门，直接关闭
                            animateRotation(doorParent, 'z', doorParent.rotation.z, originalRotation, 600);
                            doorParent.userData.isOpen = false;
                        }
                    } else {
                        // 不是机柜门，直接关闭
                        animateRotation(doorParent, 'z', doorParent.rotation.z, originalRotation, 600);
                        doorParent.userData.isOpen = false;
                    }
                    
                    closedDoorsCount++;
                }
            }
        }
    });
}

// 通用的旋转动画函数
function animateRotation(object, axis, startRotation, targetRotation, duration = 800) {
    if (!object) return;
    
    const startTime = performance.now();
    
    function animate(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数实现平滑动画
        const easeProgress = easeInOutCubic(progress);
        
        // 插值计算当前角度
        const currentRotation = startRotation + (targetRotation - startRotation) * easeProgress;
        
        // 设置旋转
        object.rotation[axis] = currentRotation;
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }
    
    requestAnimationFrame(animate);
}

// 处理主玻璃门的开关逻辑
function handleMainGlassDoor(clickedObject) {
    if (!clickedObject || !clickedObject.parent) {
        return;
    }

    const doorParent = clickedObject.parent;
    const currentRotation = doorParent.rotation.z;

    // 初始化门的状态和原始角度
    if (doorParent.userData.originalRotationZ === undefined) {
        // 第一次点击门，记录当前角度作为原始角度，默认为关闭状态
        doorParent.userData.originalRotationZ = currentRotation;
        doorParent.userData.isOpen = false;
    }

    const originalRotation = doorParent.userData.originalRotationZ;
    
    // 根据门的类型和原始角度确定打开角度 - 修正为对称的90度开启
    let openRotation = 0;
    if (clickedObject.name.includes('主玻璃门2')) {
        // 主玻璃门2：从原始位置顺时针旋转90度
        openRotation = originalRotation + Math.PI / 2; // +90度
    } else {
        // 主玻璃门1：从原始位置逆时针旋转90度  
        openRotation = originalRotation - Math.PI / 2; // -90度
    }
    const isOpen = doorParent.userData.isOpen || false;
    // 根据isOpen属性判断当前状态并切换
    let targetRotation;
    let doorAction;

    if (isOpen) {
        // 当前是打开状态，关闭门
        targetRotation = originalRotation;
        doorAction = '关闭门';
        doorParent.userData.isOpen = false;
    } else {
        // 当前是关闭状态，打开门
        targetRotation = openRotation;
        doorAction = '打开门';
        doorParent.userData.isOpen = true;
    }

    // 执行动画旋转
    const startRotation = doorParent.rotation.z;
    animateRotation(doorParent, 'z', startRotation, targetRotation, 800);
    
    console.log(`${clickedObject.name} ${doorAction}，从 ${startRotation.toFixed(3)} 旋转到 ${targetRotation.toFixed(3)}`);
}

// 处理其他玻璃门的开关逻辑
function handleGlassDoor(clickedObject) {
    if (!clickedObject || !clickedObject.parent) {
        return;
    }

    const doorParent = clickedObject.parent;
    const currentRotation = doorParent.rotation.z;

    // 根据门的类型确定打开角度
    let openRotation = 0;
    if (clickedObject.name.includes('玻璃门_1')) {
        openRotation = -Math.PI / 180 * 90; // 玻璃门_1的打开角度为90度
    } else if (clickedObject.name.includes('玻璃门1_1')) {
        openRotation = Math.PI / 180 * 90; // 玻璃门1_1的打开角度为-90度
    } else {
        openRotation = Math.PI / 180 * 90; // 默认打开角度为90度
    }

    // 初始化门的状态和原始角度
    if (doorParent.userData.originalRotationZ === undefined) {
        // 第一次点击门，记录当前角度作为原始角度，默认为关闭状态
        doorParent.userData.originalRotationZ = currentRotation;
        doorParent.userData.isOpen = false;
    }

    const originalRotation = doorParent.userData.originalRotationZ;
    const isOpen = doorParent.userData.isOpen || false;

    // 根据isOpen属性判断当前状态并切换
    let targetRotation;
    let doorAction;

    if (isOpen) {
        // 当前是打开状态，关闭门
        targetRotation = originalRotation;
        doorAction = '关闭门';
        doorParent.userData.isOpen = false;
    } else {
        // 当前是关闭状态，打开门
        targetRotation = openRotation;
        doorAction = '打开门';
        doorParent.userData.isOpen = true;
    }

    // 执行动画旋转（玻璃门使用Y轴旋转）
    const startRotation = doorParent.rotation.y;
    animateRotation(doorParent, 'y', startRotation, targetRotation, 800);

    console.log(`${clickedObject.name} ${doorAction}，从 ${startRotation.toFixed(3)} 旋转到 ${targetRotation.toFixed(3)}`);
}

// 处理机柜门的开关逻辑
// forceOpen: true-强制打开, false-强制关闭, undefined-自动切换(取反)
function handleCabinetDoor(clickedObject, forceOpen) {
    if (!clickedObject || !clickedObject.parent) {
        return;
    }

    const doorParent = clickedObject.parent;
    const currentRotation = doorParent.rotation.z;

    // 从对象名称自动识别机柜门类型并确定打开角度
    const cabinetKey = getCabinetViewKey(clickedObject.name);
    let openRotation = 0;

    if (cabinetKey && cabinetKey.startsWith('A')) {
        openRotation = Math.PI / 180; // A系列机柜门打开角度
    } else if (cabinetKey && cabinetKey.startsWith('B')) {
        openRotation = Math.PI / 180 * 180; // B系列机柜门打开角度
    } else {
        // 默认打开角度（如果无法识别机柜类型）
        openRotation = Math.PI / 180;
    }

    // 先关闭所有其他已打开的柜门
    closeAllOtherDoors(clickedObject);

    // 初始化门的状态和原始角度
    if (doorParent.userData.originalRotationZ === undefined) {
        // 第一次点击门，记录当前角度作为原始角度，默认为关闭状态
        doorParent.userData.originalRotationZ = currentRotation;
        doorParent.userData.isOpen = false;
    }

    const originalRotation = doorParent.userData.originalRotationZ;


    // 通过比较当前角度与原始角度的差异来判断门的真实状态
    // 设置一个小的容差值来处理浮点数精度问题
    const tolerance = 0.01;
    const angleDiff = Math.abs(currentRotation - originalRotation);
    const isActuallyOpen = angleDiff > tolerance;

    // 更新状态标志以确保一致性
    doorParent.userData.isOpen = isActuallyOpen;
    
    // 根据参数或实际角度状态判断目标状态
    let targetRotation;
    let doorAction;
    let shouldOpen;

    if (forceOpen !== undefined) {
        // 有明确的开关指令
        shouldOpen = forceOpen;
    } else {
        // 没有明确指令，使用取反逻辑
        shouldOpen = !isActuallyOpen;
    }

    if (shouldOpen) {
        // 打开门
        targetRotation = openRotation;
        doorAction = '打开门';
        doorParent.userData.isOpen = true;
        
        // 执行动画旋转
        const startRotation = doorParent.rotation.z;
        animateRotation(doorParent, 'z', startRotation, targetRotation, 800);
        
        console.log(`${doorAction}: ${clickedObject.name}，从 ${startRotation.toFixed(3)} 旋转到 ${targetRotation.toFixed(3)}`);
    } else {
        // 关闭门
        targetRotation = originalRotation;
        doorAction = '关闭门';
        
        // 先收回该机柜中的所有抽出设备，等待完成后再关门
        if (cabinetKey) {
            console.log(`🔄 关闭机柜门，先收回机柜 ${cabinetKey} 中的所有设备`);
            retractAllDevicesInCabinet(cabinetKey).then(() => {
                // 设备收回完成后，再关门
                doorParent.userData.isOpen = false;
                
                // 如果关闭的是当前选中机柜的门，恢复交互状态
                if (selectedCabinetKey.value === cabinetKey) {
                    console.log(`🔓 机柜门关闭，恢复机柜交互状态: ${cabinetKey}`);
                    selectedCabinetKey.value = null;
                }
                
                // 执行门关闭动画
                const startRotation = doorParent.rotation.z;
                animateRotation(doorParent, 'z', startRotation, targetRotation, 800);
                
                console.log(`${doorAction}: ${clickedObject.name}，从 ${startRotation.toFixed(3)} 旋转到 ${targetRotation.toFixed(3)}`);
            });
        } else {
            // 如果没有机柜key，直接关门
            doorParent.userData.isOpen = false;
            
            // 执行门关闭动画
            const startRotation = doorParent.rotation.z;
            animateRotation(doorParent, 'z', startRotation, targetRotation, 800);
            
            console.log(`${doorAction}: ${clickedObject.name}，从 ${startRotation.toFixed(3)} 旋转到 ${targetRotation.toFixed(3)}`);
        }
    }
}

// 关闭所有门（包括主玻璃门、其他玻璃门和机柜门）
function closeAllDoors() {
    if (!model) {
        console.warn('模型未加载，无法关闭门');
        return;
    }

    let closedDoorsCount = 0;

    // 遍历模型中的所有对象，查找所有门对象
    model.traverse((child) => {
        // 检查是否是门对象（包含DOOR关键词或各种玻璃门）
        const isDoor = child.name && child.name.toUpperCase().includes('DOOR');
        const isMainGlassDoor = child.name && child.name.includes('主玻璃门');
        const isOtherGlassDoor = child.name && (child.name.includes('玻璃门_1') || child.name.includes('玻璃门1_1'));

        if (isDoor || isMainGlassDoor || isOtherGlassDoor) {
            const doorParent = child.parent;
            if (doorParent && doorParent.userData.originalRotationZ !== undefined) {
                // 检查门的isOpen状态
                const isOpen = doorParent.userData.isOpen || false;

                if (isOpen) {
                    // 门已打开，先收回设备再关闭门
                    const originalRotation = doorParent.userData.originalRotationZ;
                    
                    // 如果是机柜门，先收回对应机柜的设备
                    if (isDoor && child.name.toUpperCase().includes('DOOR')) {
                        const cabinetKey = getCabinetViewKey(child.name);
                        if (cabinetKey) {
                            console.log(`🔄 关闭所有门，先收回机柜 ${cabinetKey} 中的所有设备`);
                            retractAllDevicesInCabinet(cabinetKey).then(() => {
                                // 设备收回完成后，再关闭机柜门
                                const currentZRotation = doorParent.rotation.z;
                                animateRotation(doorParent, 'z', currentZRotation, originalRotation, 600);
                                doorParent.userData.isOpen = false;
                                console.log(`动画关闭机柜门: ${child.name}`);
                            });
                        } else {
                            // 机柜门但没有key，直接关闭
                            const currentZRotation = doorParent.rotation.z;
                            animateRotation(doorParent, 'z', currentZRotation, originalRotation, 600);
                            doorParent.userData.isOpen = false;
                            console.log(`动画关闭门: ${child.name}`);
                        }
                    } else {
                        // 其他类型的门（玻璃门等），使用正常的点击关闭逻辑
                        if (child.name && child.name.includes('主玻璃门')) {
                            // 主玻璃门：使用正常的点击关闭逻辑，确保状态一致
                            console.log(`🔄 复位关闭主玻璃门: ${child.name}`);
                            handleMainGlassDoor(child);
                        } else if (child.name && (child.name.includes('玻璃门_1') || child.name.includes('玻璃门1_1'))) {
                            // 其他玻璃门：使用正常的点击关闭逻辑
                            console.log(`🔄 复位关闭玻璃门: ${child.name}`);
                            handleGlassDoor(child);
                        } else {
                            // 其他门类型，直接设置角度
                            const currentZRotation = doorParent.rotation.z;
                            animateRotation(doorParent, 'z', currentZRotation, originalRotation, 600);
                            doorParent.userData.isOpen = false;
                            console.log(`动画关闭门: ${child.name}`);
                        }
                    }
                    
                    closedDoorsCount++;
                }
            }
        }
    });

    console.log(`共关闭了 ${closedDoorsCount} 扇门`);
    
    // 关闭所有门后，收回所有设备并恢复交互状态
    if (closedDoorsCount > 0) {
        console.log(`🔓 所有门已关闭，收回所有设备并恢复机柜交互状态`);
        
        // 收回当前选中机柜的所有设备
        if (selectedCabinetKey.value) {
            retractAllDevicesInCabinet(selectedCabinetKey.value);
        }
        
        selectedCabinetKey.value = null;
    }
}

// 找到机柜的父对象（用于机柜开门功能）
function findCabinetParent(object) {
    let current = object;

    // 向上遍历，找到包含机柜门的对象
    while (current) {
        // 优先检查是否是机柜门（A0-DOOR, B0-DOOR 格式）
        if (current.name && /^[AB]\d+-DOOR$/i.test(current.name)) {
            return current;
        }

        // 检查是否包含DOOR关键词的机柜门
        if (current.name && current.name.toLowerCase().includes('door')) {
            // 进一步检查是否符合机柜门的命名规范
            if (/[AB]\d+.*door/i.test(current.name)) {
                return current;
            }
        }

        // 检查parent对象
        if (current.parent) {
            // 检查parent的name是否是机柜门
            if (current.parent.name && /^[AB]\d+-DOOR$/i.test(current.parent.name)) {
                return current.parent;
            }

            // 检查parent是否包含DOOR关键词
            if (current.parent.name && current.parent.name.toLowerCase().includes('door')) {
                if (/[AB]\d+.*door/i.test(current.parent.name)) {
                    return current.parent;
                }
            }
        }

        current = current.parent;
    }
    return object; // 如果没找到合适的门对象，返回原对象
}

// 添加存储空间柱体效果
function addStorageSpaceEffect() {
    if (!model) return;

    // 定义机柜存储空间数据（模拟数据，百分比）
    const cabinetStorageData = [
        { name: 'IT柜_001', usage: 80 }, // 85%使用率
        { name: 'IT柜_015', usage: 80 }, // 85%使用率
    ];

    // 柱体参数
    const cylinderParams = {
        outerRadius: 0.2,      // 外层柱体半径
        innerRadius: 0.15,     // 内层柱体半径
        maxHeight: 2,          // 最大高度
        segments: 16           // 圆形分段数
    };

    // 存储所有存储空间效果
    const storageEffects = [];

    // 查找所有IT柜并添加存储空间效果
    model.traverse((child) => {
        if (child.name && child.name.includes('IT柜')) {
            const storageData = cabinetStorageData.find(item => item.name === child.name);
            if (storageData) {
                // 创建外层柱体（透明玻璃材质）
                const outerGeometry = new THREE.CylinderGeometry(
                    cylinderParams.outerRadius,
                    cylinderParams.outerRadius,
                    cylinderParams.maxHeight,
                    cylinderParams.segments
                );

                const outerMaterial = new THREE.MeshPhysicalMaterial({
                    color: 0xffffff,
                    transparent: true,
                    opacity: 0.2,
                    roughness: 0.0,
                    metalness: 0.0,
                    clearcoat: 1.0,
                    clearcoatRoughness: 0.0,
                    transmission: 0.9, // 透射效果
                    thickness: 0.5,    // 厚度
                });

                const outerCylinder = new THREE.Mesh(outerGeometry, outerMaterial);

                // 计算内层柱体高度（基于使用率）
                const innerHeight = (storageData.usage / 100) * cylinderParams.maxHeight;

                // 创建内层柱体（黄色发光）
                const innerGeometry = new THREE.CylinderGeometry(
                    cylinderParams.innerRadius,
                    cylinderParams.innerRadius,
                    innerHeight,
                    cylinderParams.segments
                );

                const innerMaterial = new THREE.MeshStandardMaterial({
                    color: 0xFFA571,
                    emissive: 0xFA8466,
                    emissiveIntensity: 0.6,
                    transparent: false
                });

                const innerCylinder = new THREE.Mesh(innerGeometry, innerMaterial);

                // 计算机柜的边界盒来获取正确的中心位置
                const cabinetBox = new THREE.Box3().setFromObject(child);
                const cabinetCenter = cabinetBox.getCenter(new THREE.Vector3());
                const cabinetSize = cabinetBox.getSize(new THREE.Vector3());

                // 定位柱体在机柜正上方
                const position = cabinetCenter.clone();
                position.y = cabinetBox.max.y + cylinderParams.maxHeight / 2 + 0.1; // 在机柜顶部上方

                outerCylinder.position.copy(position);
                innerCylinder.position.copy(position);

                // 内层柱体需要向下偏移，因为它的高度可能小于外层
                innerCylinder.position.y -= (cylinderParams.maxHeight - innerHeight) / 2;

                // 添加到场景
                scene.add(outerCylinder);
                scene.add(innerCylinder);

                // 创建存储使用率标签
                const usageLabel = createStorageLabel(child.name);
                usageLabel.position.copy(position);
                usageLabel.position.y += cylinderParams.maxHeight / 2 + 0.5;
                usageLabel.userData = { type: 'storage-label', cabinetName: child.name };
                scene.add(usageLabel);

                // 添加轻微的点光源增强效果
                const pointLight = new THREE.PointLight(0xFA8466, 0.8, 5);
                pointLight.position.copy(position);
                pointLight.position.y -= cylinderParams.maxHeight / 2 + innerHeight / 2; // 位于内层柱体中心
                scene.add(pointLight);

                // 存储效果信息
                storageEffects.push({
                    cabinet: child,
                    outerCylinder,
                    innerCylinder,
                    usageLabel,
                    pointLight,
                    outerGeometry,
                    innerGeometry,
                    outerMaterial,
                    innerMaterial,
                    usage: storageData.usage
                });
            }
        }
    });

    // 将相关对象存储到全局调试对象中
    if (window.threeDebug) {
        window.threeDebug.storageEffects = storageEffects;
        window.threeDebug.removeStorageEffect = () => {
            storageEffects.forEach(effect => {
                // 移除所有对象
                scene.remove(effect.outerCylinder);
                scene.remove(effect.innerCylinder);
                scene.remove(effect.usageLabel);
                scene.remove(effect.pointLight);

                // 清理几何体和材质
                effect.outerGeometry.dispose();
                effect.innerGeometry.dispose();
                effect.outerMaterial.dispose();
                effect.innerMaterial.dispose();

                if (effect.usageLabel.material && effect.usageLabel.material.map) {
                    effect.usageLabel.material.map.dispose();
                    effect.usageLabel.material.dispose();
                }
            });

            // 清空数组
            storageEffects.length = 0;
        };

        // 更新存储使用率的方法
        window.threeDebug.updateStorageUsage = (newUsageData) => {
            // 先移除现有效果
            window.threeDebug.removeStorageEffect();
            // 更新使用率数据
            if (newUsageData && Array.isArray(newUsageData)) {
                cabinetStorageData.splice(0, cabinetStorageData.length, ...newUsageData);
            }
            // 重新添加效果
            addStorageSpaceEffect();
        };
    }
}

// 创建存储使用率标签
function createStorageLabel(name) {
    let html = `
        <div class="storage-label">
            <div class="storage-text">${name}</div>
        </div>
    `;
    var dom = document.createElement('div');
    dom.innerHTML = html;

    // 创建CSS2DObject
    const label = new CSS2DObject(dom);
    label.userData = { type: 'storage-label', text: name };
    label.element = dom;

    return label;
}

// 漫游功能
function addManyou() {
    if (!camera || !controls) {
        console.warn('相机或控制器未初始化，无法开始漫游');
        return;
    }

    // 记录初始视角位置
    const initialState = {
        camera: {
            x: camera.position.x,
            y: camera.position.y,
            z: camera.position.z
        },
        target: {
            x: controls.target.x,
            y: controls.target.y,
            z: controls.target.z
        }
    };
    const points = [
        // {
        //     camera: { x: 3.990657220902234, y: 1.4636304805479183, z: -1.2953292342508467 },
        //     target: { x: -0.6591188135756444, y: 0.40012104075093186, z: -1.2798734138005994 },
        //     time: 2000
        // },
        // A系列机柜漫游路径 (A0-A9)
        {
            camera: { x: 2.527035, y: 1.689322, z: 1.172707 },
            target: { x: 2.343369, y: 1.265281, z: 3.708394 },
            time: 1500
        },
        {
            camera: { x: 1.86965, y: 1.730524, z: 1.168938 },
            target: { x: 1.833986, y: 1.246823, z: 3.700343 },
            time: 1500
        },
        {
            camera: { x: 1.265625, y: 1.751481, z: 1.164403 },
            target: { x: 1.229855, y: 1.268398, z: 3.695924 },
            time: 1500
        },
        {
            camera: { x: 0.654083, y: 1.769479, z: 1.159196 },
            target: { x: 0.618314, y: 1.286396, z: 3.690718 },
            time: 1500
        },
        {
            camera: { x: 0.063987, y: 1.780274, z: 1.152919 },
            target: { x: 0.028218, y: 1.297191, z: 3.68444 },
            time: 1500
        },
        {
            camera: { x: -0.499859, y: 1.74057, z: 1.137375 },
            target: { x: -0.535628, y: 1.257487, z: 3.668896 },
            time: 1500
        },
        {
            camera: { x: -1.103876, y: 1.729707, z: 1.126768 },
            target: { x: -1.139645, y: 1.246624, z: 3.658289 },
            time: 1500
        },
        {
            camera: { x: -1.707944, y: 1.726247, z: 1.117572 },
            target: { x: -1.743713, y: 1.243164, z: 3.649094 },
            time: 1500
        },
        {
            camera: { x: -2.305987, y: 1.73388, z: 1.110579 },
            target: { x: -2.341756, y: 1.250797, z: 3.6421 },
            time: 1500
        },
        {
            camera: { x: -2.887556, y: 1.741554, z: 1.103768 },
            target: { x: -2.908548, y: 1.229046, z: 3.629663 },
            time: 1500
        },
        // 过渡点：从A系列到B系列
        // {
        //     camera: { x: 0, y: 3, z: 2 },
        //     target: { x: 0, y: 1, z: 2 },
        //     time: 2000
        // },
        // B系列机柜漫游路径 (B0-B9)
        {
            camera: { x: 2.431052, y: 1.751989, z: 2.3032 },
            target: { x: 2.435205, y: 1.364984, z: 0.637643 },
            time: 1500
        },
        {
            camera: { x: 1.828284, y: 1.756427, z: 2.300666 },
            target: { x: 1.832437, y: 1.369422, z: 0.635109 },
            time: 1500
        },
        {
            camera: { x: 1.235194, y: 1.76118, z: 2.298083 },
            target: { x: 1.239346, y: 1.374175, z: 0.632526 },
            time: 1500
        },
        {
            camera: { x: 0.644658, y: 1.763375, z: 2.296074 },
            target: { x: 0.639297, y: 1.37637, z: 0.63052 },
            time: 1500
        },
        {
            camera: { x: 0.042079, y: 1.758641, z: 2.299177 },
            target: { x: 0.036541, y: 1.371636, z: 0.633624 },
            time: 1500
        },
        {
            camera: { x: -0.52646, y: 1.753908, z: 2.302167 },
            target: { x: -0.531997, y: 1.366903, z: 0.636614 },
            time: 1500
        },
        {
            camera: { x: -1.114277, y: 1.756407, z: 2.303541 },
            target: { x: -1.119815, y: 1.369402, z: 0.637988 },
            time: 1500
        },
        {
            camera: { x: -1.710293, y: 1.751541, z: 2.306548 },
            target: { x: -1.73457, y: 1.364536, z: 0.641162 },
            time: 1500
        },
        {
            camera: { x: -2.300238, y: 1.746793, z: 2.316478 },
            target: { x: -2.325155, y: 1.359788, z: 0.651102 },
            time: 1500
        },
        {
            camera: { x: -2.898133, y: 1.746808, z: 2.32542 },
            target: { x: -2.923049, y: 1.359803, z: 0.660043 },
            time: 1500
        }
    ];

    let currentPointIndex = 0;
    let isRoaming = false;
    let roamingAnimationId = null;

    // 平滑移动到指定点
    function moveToPoint(point, duration) {
        return new Promise((resolve) => {
            const startTime = performance.now();
            const startCameraPos = camera.position.clone();
            const startTargetPos = controls.target.clone();

            const targetCameraPos = new THREE.Vector3(point.camera.x, point.camera.y, point.camera.z);
            const targetControlPos = new THREE.Vector3(point.target.x, point.target.y, point.target.z);

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数实现更平滑的移动
                const easeProgress = easeInOutCubic(progress);

                // 插值计算相机位置
                camera.position.lerpVectors(startCameraPos, targetCameraPos, easeProgress);

                // 插值计算控制器目标位置
                controls.target.lerpVectors(startTargetPos, targetControlPos, easeProgress);

                // 更新控制器
                controls.update();

                if (progress < 1) {
                    roamingAnimationId = requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            }

            roamingAnimationId = requestAnimationFrame(animate);
        });
    }



    // 回到初始视角
    async function returnToInitialView() {
        // 使用较长的时间实现更平滑的返回
        await moveToPoint(initialState, 2000);
    }

    // 开始漫游
    async function startRoaming() {
        if (isRoaming) {
            return;
        }

        isRoaming = true;
        currentPointIndex = 0;

        try {
            for (let i = 0; i < points.length; i++) {
                if (!isRoaming) break; // 检查是否被停止

                currentPointIndex = i;
                const point = points[i];
                await moveToPoint(point, point.time);

                // 在每个点停留一小段时间（可选）
                if (i < points.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            // 漫游完成后，停留一小段时间，然后返回初始视角
            if (isRoaming) {
                console.log('漫游路径完成，准备返回初始视角...');
                await new Promise(resolve => setTimeout(resolve, 1000));

                if (isRoaming) {
                    await returnToInitialView();
                }
            }

            console.log('漫游完全结束');
        } catch (error) {
            console.error('漫游过程中出错:', error);
        } finally {
            isRoaming = false;
        }
    }

    // 停止漫游
    function stopRoaming() {
        isRoaming = false;
        if (roamingAnimationId) {
            cancelAnimationFrame(roamingAnimationId);
            roamingAnimationId = null;
        }
        console.log('漫游已停止');
    }

    // 跳转到指定点
    function jumpToPoint(index) {
        if (index >= 0 && index < points.length) {
            const point = points[index];
            camera.position.set(point.camera.x, point.camera.y, point.camera.z);
            controls.target.set(point.target.x, point.target.y, point.target.z);
            controls.update();
            console.log(`已跳转到第 ${index + 1} 个点`);
        } else {
            console.warn('无效的点索引:', index);
        }
    }

    // 获取当前相机和控制器状态
    function getCurrentState() {
        return {
            camera: {
                x: camera.position.x,
                y: camera.position.y,
                z: camera.position.z
            },
            target: {
                x: controls.target.x,
                y: controls.target.y,
                z: controls.target.z
            }
        };
    }

    // 将漫游功能暴露到全局调试对象
    if (window.threeDebug) {
        window.threeDebug.roaming = {
            start: startRoaming,
            stop: stopRoaming,
            jumpToPoint: jumpToPoint,
            returnToInitial: returnToInitialView,
            getCurrentState: getCurrentState,
            getInitialState: () => initialState,
            isRoaming: () => isRoaming,
            getCurrentPointIndex: () => currentPointIndex,
            getPoints: () => points,
            getTotalPoints: () => points.length
        };
    }

    // 立即开始漫游
    startRoaming();
}

/**
 * 计算设备的视角位置
 * @param {THREE.Object3D} deviceObject - 设备对象
 * @param {string} cabinetName - 机柜名称
 * @returns {Object} 包含camera和target位置的对象
 */
function calculateDeviceViewPosition(deviceObject, cabinetName) {
    if (!deviceObject) {
        console.warn('❌ 设备对象为空，无法计算视角位置');
        return null;
    }

    // 获取设备的世界位置
    const deviceWorldPos = new THREE.Vector3();
    deviceObject.getWorldPosition(deviceWorldPos);

    // 根据机柜名称确定相机距离和角度
    let cameraDistance = 1.0; // 默认相机距离
    let cameraHeight = 0.5; // 相机高度偏移
    let cameraOffset = new THREE.Vector3(0, 0, 0); // 相机位置偏移

    if (cabinetName && cabinetName.startsWith('A')) {
        // A系列机柜，相机在设备后方（z轴负方向）看向设备正面
        cameraOffset.set(0, cameraHeight, -cameraDistance);
        console.log(`📹 A系列机柜设备视角: 相机在设备后方看正面`);
    } else if (cabinetName && cabinetName.startsWith('B')) {
        // B系列机柜，相机在设备前方（z轴正方向）看向设备正面
        cameraOffset.set(0, cameraHeight, cameraDistance);
        console.log(`📹 B系列机柜设备视角: 相机在设备前方看正面`);
    } else {
        // 其他情况，默认在设备前方
        cameraOffset.set(0, cameraHeight, cameraDistance);
        console.log(`📹 默认设备视角: 相机在设备前方`);
    }

    // 计算相机位置
    const cameraPosition = deviceWorldPos.clone().add(cameraOffset);
    
    // target位置就是设备位置，稍微向上偏移
    const targetPosition = deviceWorldPos.clone().add(new THREE.Vector3(0, 0.2, 0));

    console.log(`📷 计算得到的相机位置:`, cameraPosition);
    console.log(`🎯 计算得到的target位置:`, targetPosition);

    return {
        camera: {
            x: cameraPosition.x,
            y: cameraPosition.y,
            z: cameraPosition.z
        },
        target: {
            x: targetPosition.x,
            y: targetPosition.y,
            z: targetPosition.z
        }
    };
}

/**
 * 移动相机到设备面前
 * @param {THREE.Object3D} deviceObject - 设备对象
 * @param {string} cabinetName - 机柜名称
 * @param {number} duration - 动画持续时间
 * @returns {Promise} 移动动画的Promise
 */
function moveToDeviceView(deviceObject, cabinetName, duration = 1000) {
    const deviceViewPosition = calculateDeviceViewPosition(deviceObject, cabinetName);
    
    if (!deviceViewPosition) {
        console.warn('❌ 无法计算设备视角位置');
        return Promise.reject('无法计算设备视角位置');
    }

    console.log(`🎬 开始移动相机到设备面前:`, deviceObject.name);
    return smoothMoveCamera(deviceViewPosition, duration);
}

// 独立的平滑移动方法
function smoothMoveCamera(targetView, duration = 1500) {
    if (!camera || !controls) {
        console.warn('相机或控制器未初始化，无法移动相机');
        return Promise.reject('相机或控制器未初始化');
    }

    return new Promise((resolve) => {
        const startTime = performance.now();
        const startCameraPos = camera.position.clone();
        const startTargetPos = controls.target.clone();

        const targetCameraPos = new THREE.Vector3(targetView.camera.x, targetView.camera.y, targetView.camera.z);
        const targetControlPos = new THREE.Vector3(targetView.target.x, targetView.target.y, targetView.target.z);

        let smoothAnimationId = null;

        function animate(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数实现更平滑的移动
            const easeProgress = easeInOutCubic(progress);

            // 插值计算相机位置
            camera.position.lerpVectors(startCameraPos, targetCameraPos, easeProgress);

            // 插值计算控制器目标位置
            controls.target.lerpVectors(startTargetPos, targetControlPos, easeProgress);

            // 更新控制器
            controls.update();

            if (progress < 1) {
                smoothAnimationId = requestAnimationFrame(animate);
            } else {
                resolve();
            }
        }

        smoothAnimationId = requestAnimationFrame(animate);
    });
}

// 缓动函数
function easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}

// 清除场景中除模型外的其他元素
function clearScene() {
    if (!scene) return;
    // 记录需要保留的基础元素
    const preserveElements = new Set();

    // 保留模型
    if (model) {
        preserveElements.add(model);
    }

    // 保留基础光源
    scene.children.forEach(child => {
        if (child.type === 'AmbientLight' ||
            child.type === 'DirectionalLight' ||
            child.type === 'HemisphereLight') {
            preserveElements.add(child);
        }
    });

    // 收集需要移除的元素
    const toRemove = [];

    scene.children.forEach(child => {
        if (!preserveElements.has(child)) {
            // 检查是否是设备标签（平面）
            if (child.type === 'Mesh' && child.userData.type === 'plane-label') {
                // 设备标签只隐藏，不移除
                child.visible = false;
            } else if (child.type === 'Sprite' && child.userData.type === 'sprite-label') {
                // 精灵标签只隐藏，不移除
                child.visible = false;
            } else if (child.userData && child.userData.type === 'storage-label') {
                // 存储标签标记为需要移除
                toRemove.push(child);
            } else {
                // 其他元素标记为需要移除
                toRemove.push(child);
            }
        }
    });

    // 移除标记的元素
    toRemove.forEach(child => {
        scene.remove(child);

        // 清理几何体和材质
        if (child.geometry) {
            child.geometry.dispose();
        }
        if (child.material) {
            if (Array.isArray(child.material)) {
                child.material.forEach(material => {
                    if (material.map) material.map.dispose();
                    material.dispose();
                });
            } else {
                if (child.material.map) child.material.map.dispose();
                child.material.dispose();
            }
        }

        // 特别处理平面标签的纹理清理
        if (child.type === 'Mesh' && child.userData.type === 'plane-label') {
            if (child.userData.texture) child.userData.texture.dispose();
            if (child.userData.geometry) child.userData.geometry.dispose();
        }
    });

    // 恢复高温机柜的原始材质
    if (window.threeDebug && window.threeDebug.removeHighTempEffect) {
        window.threeDebug.removeHighTempEffect();
    }

    // 清理调试对象中的引用
    if (window.threeDebug) {
        delete window.threeDebug.heatmap;
        delete window.threeDebug.heatmapCanvas;
        delete window.threeDebug.storageEffects;
        delete window.threeDebug.updateHeatmap;
        delete window.threeDebug.toggleHeatmap;
        delete window.threeDebug.setHeatmapOpacity;
        delete window.threeDebug.removeHeatmap;
        delete window.threeDebug.removeHighTempEffect;
        delete window.threeDebug.removeStorageEffect;
        delete window.threeDebug.updateStorageUsage;
    }
}

// 显示所有设备标签
function showAllDeviceLabels() {
    if (!scene) return;

    scene.children.forEach(child => {
        if ((child.type === 'Mesh' && child.userData.type === 'plane-label') ||
            (child.type === 'Sprite' && child.userData.type === 'sprite-label')) {
            child.visible = true;
        }
    });
}

// 隐藏所有设备标签
function hideAllDeviceLabels() {
    if (!scene) return;

    scene.children.forEach(child => {
        if ((child.type === 'Mesh' && child.userData.type === 'plane-label') ||
            (child.type === 'Sprite' && child.userData.type === 'sprite-label')) {
            child.visible = false;
        }
    });
}

// 处理设备选择变化
function handleDeviceSelectionChanged(event) {
    const { selectedDevices, showAll } = event.detail;
    
    console.log('设备选择变化事件:', { selectedDevices, showAll });
    
    // 设备类型到模型中实际对象名称的映射关系
    const deviceModelMapping = {
        '摄像头': ['SXT_002', 'SXT_003', 'SXT_004', 'SXT_005', 'SXT_006', 'SXT_008'],
        '列间空调': ['LJKT_001', 'LJKT_002', 'LJKT_003'],
        '门禁': ['MJ_001', 'MJ_002'],
        '配电柜': ['PDG_001', 'PDG_002', 'PDG_003'],
        '机房空调': ['JFKT_001', 'JFKT_002'],
        '精密列头柜': [], // 如果有对应的模型名称，请补充
        '温湿度设备': [], // 如果有对应的模型名称，请补充
        '漏水': [], // 如果有对应的模型名称，请补充
        '烟感': [] // 如果有对应的模型名称，请补充
    };

    // 收集所有需要显示的模型对象名称
    const targetObjectNames = [];
    selectedDevices.forEach(selectedType => {
        if (deviceModelMapping[selectedType]) {
            targetObjectNames.push(...deviceModelMapping[selectedType]);
        }
    });

    console.log('目标对象名称列表:', targetObjectNames);

    // 如果没有模型加载，直接返回
    if (!model) {
        console.warn('模型未加载，无法控制设备显示');
        return;
    }

    // 遍历模型中的所有对象，控制指定名称对象的可见性
    model.traverse(child => {
        // 检查对象名称是否在目标列表中
        if (child.name && targetObjectNames.includes(child.name)) {
            if (showAll || selectedDevices.length > 0) {
                child.visible = true;
                console.log('显示设备:', child.name);
            } else {
                child.visible = false;
                console.log('隐藏设备:', child.name);
            }
        } else if (child.name && Object.values(deviceModelMapping).flat().includes(child.name)) {
            // 如果是已知的设备对象但不在当前选中列表中
            if (!showAll) {
                child.visible = false;
                console.log('隐藏未选中设备:', child.name);
            } else {
                child.visible = true;
                console.log('显示所有设备:', child.name);
            }
        }
    });
    
    console.log('设备显示/隐藏控制完成');
}

/**
 * 处理设备选择事件 - 处理数字ID并从机柜信息构造完整设备ID
 * @param {CustomEvent} event - 包含设备ID和设备名称的事件对象
 */
function handleDeviceSelected(event) {
    const { deviceId, deviceName, cabinetName } = event.detail;
    console.log('📟 接收到设备选择事件:', { deviceId, deviceName, cabinetName });
    
    // 1. 首先从cabinetName中提取机柜编号
    let cabinetNumber = null;
    let cabinetKey = null;
    
    if (cabinetName) {
        // 从机柜名称中提取机柜编号，如 "A1机柜" -> "A1"
        const cabinetMatch = cabinetName.match(/([AB]\d+)/i);
        if (cabinetMatch) {
            cabinetNumber = cabinetMatch[1].toUpperCase();
            cabinetKey = getCabinetViewKey(cabinetNumber);
            console.log('✅ 从机柜名称提取机柜编号:', cabinetNumber);
        }
    }
    
    // 2. 如果没有找到机柜信息，尝试解析deviceId（如果是A1-SB001格式）
    if (!cabinetKey) {
        const parsedResult = parseDeviceId(deviceId);
        if (parsedResult.isValid) {
            cabinetNumber = parsedResult.cabinetNumber;
            cabinetKey = getCabinetViewKey(cabinetNumber);
            console.log('✅ 从设备ID解析机柜编号:', cabinetNumber);
        }
    }
    
    // 3. 如果仍然没有找到机柜，使用默认机柜A1
    if (!cabinetKey) {
        console.warn('⚠️ 无法确定机柜，使用默认机柜A1');
        cabinetNumber = 'A1';
        cabinetKey = getCabinetViewKey(cabinetNumber);
    }
    
    if (!cabinetKey) {
        console.error(`❌ 未找到机柜配置: ${cabinetNumber}`);
        return;
    }
    
    // 4. 构造设备编号
    let deviceNumber = deviceId;
    let fullDeviceId = deviceId;
    
    // 如果deviceId是纯数字，构造标准格式
    if (/^\d+$/.test(String(deviceId))) {
        deviceNumber = `SB${String(deviceId).padStart(3, '0')}`; // 4 -> SB004
        fullDeviceId = `${cabinetNumber}-${deviceNumber}`; // A1-SB004
        console.log('🔧 构造设备标识:', {
            原始ID: deviceId,
            设备编号: deviceNumber,
            完整ID: fullDeviceId
        });
    }
    
    // 使用现有的cabinetViewPositions获取视角
    const targetView = cabinetViewPositions[cabinetKey];
    if (!targetView) {
        console.error(`❌ 未找到机柜视角配置: ${cabinetKey}`);
        return;
    }
    
    console.log(`🚀 开始机柜设备操作: ${fullDeviceId} -> 机柜 ${cabinetKey} -> 设备 ${deviceNumber}`);
    
    // 1. 使用现有的smoothMoveCamera方法定位到机柜视角
    console.log('📹 定位到机柜视角...');
    smoothMoveCamera(targetView, 1500).then(() => {
        console.log(`✅ 相机已定位到机柜 ${cabinetKey}`);
        
        // 2. 使用现有的开门逻辑
        const doorName = `${cabinetKey}-DOOR_4`;
        let doorObject = null;
        
        if (model) {
            model.traverse((child) => {
                if (child.name === doorName) {
                    doorObject = child;
                }
            });
            
            if (doorObject) {
                console.log('🚪 打开机柜门...');
                // 使用现有的handleCabinetDoor方法强制打开门
                handleCabinetDoor(doorObject, true);
                
                // 3. 延迟执行设备抽出，等待开门完成
                setTimeout(() => {
                    console.log('📦 查找并抽出设备...');
                    triggerExistingDeviceClick(cabinetKey, deviceNumber, fullDeviceId);
                }, 2000);
            } else {
                console.warn(`⚠️ 未找到机柜门: ${doorName}`);
                // 即使没有门，也尝试抽出设备
                setTimeout(() => {
                    console.log('📦 查找并抽出设备...');
                    triggerExistingDeviceClick(cabinetKey, deviceNumber, fullDeviceId);
                }, 500);
            }
        }
    }).catch((error) => {
        console.error('❌ 相机定位失败:', error);
    });
}

/**
 * 触发现有设备点击逻辑 - 直接复制设备点击方法
 * @param {string} cabinetKey - 机柜编号 (如：A1)
 * @param {string} deviceNumber - 设备编号 (如：SB001)
 * @param {string} fullDeviceId - 完整设备ID (如：A1-SB001)
 */
function triggerExistingDeviceClick(cabinetKey, deviceNumber, fullDeviceId) {
    if (!model) {
        console.warn('⚠️ 3D模型未加载');
        return;
    }
    
    console.log(`🎯 开始查找设备: ${deviceNumber} 在机柜: ${cabinetKey} (完整ID: ${fullDeviceId})`);
    
    // **关键步骤1**: 设置选中的机柜（这是现有逻辑的前提条件）
    selectedCabinetKey.value = cabinetKey;
    console.log(`🔐 设置选中机柜: ${cabinetKey}`);
    
    // **关键步骤2**: 直接使用deviceId查找设备对象
    let deviceObject = null;
    
    model.traverse((child) => {
        if (deviceObject) return; // 已找到，跳过
        
        if (child.name === String(fullDeviceId)) {
            deviceObject = child;
            console.log(`🎯 找到设备对象: ${child.name}`);
            return;
        }
    });
    
    if (deviceObject) {
        console.log(`✅ 成功找到设备，直接复制设备点击逻辑`);
        
        // **直接复制handleCabinetDeviceClick的逻辑**
        console.log(`🔧 设备点击处理开始! 设备名称: ${deviceObject.name}`);
        console.log(`📋 当前选中的机柜: ${selectedCabinetKey.value}`);
        
        // 调试：检查找到的设备对象信息
        console.log(`🔍 找到的设备对象详细信息:`, {
            名称: deviceObject.name,
            类型: deviceObject.type,
            位置: deviceObject.position,
            父对象: deviceObject.parent?.name || '无',
            子对象数量: deviceObject.children?.length || 0
        });
        
        // 直接使用找到的设备对象，不查找父对象
        const deviceToMove = deviceObject;
        
        console.log(`🎯 将要移动的对象:`, {
            名称: deviceToMove.name,
            类型: deviceToMove.type,
            当前位置: deviceToMove.position,
            是否是模型根: deviceToMove === model,
            是否是场景根: deviceToMove === scene
        });
        
        // 如果误选到了模型根对象，停止操作
        if (deviceToMove === model || deviceToMove === scene) {
            console.error('❌ 错误：选中了模型根对象或场景根对象，停止操作');
            return;
        }
        
        // 保存原始位置（如果还没有保存的话）
        if (deviceToMove.userData.originalPosition === undefined) {
            deviceToMove.userData.originalPosition = deviceToMove.position.clone();
            console.log(`💾 保存设备原始位置: ${deviceToMove.name}, z: ${deviceToMove.position.z}`);
        }
        
        // 检查设备是否已经被抽出（从设备整体对象获取状态）
        const isExtended = deviceToMove.userData.isExtended || false;
        
        // 根据机柜名称决定移动方向
        const cabinetName = selectedCabinetKey.value;
        let targetZOffset = 0;
        
        if (cabinetName && cabinetName.startsWith('B')) {
            // A开头的机柜，设备向前抽出
            targetZOffset = isExtended ? 0 : 0.3;
            console.log(`📈 A系列机柜设备${isExtended ? '收回' : '抽出'}，z轴${isExtended ? '恢复' : '增加0.3'}`);
        } else if (cabinetName && cabinetName.startsWith('A')) {
            // B开头的机柜，设备向后抽出
            targetZOffset = isExtended ? 0 : -0.3;
            console.log(`📉 B系列机柜设备${isExtended ? '收回' : '抽出'}，z轴${isExtended ? '恢复' : '减少0.3'}`);
        } else {
            console.warn(`❓ 未知的机柜类型: ${cabinetName}`);
            return;
        }
        
        // 计算目标位置
        const originalPosition = deviceToMove.userData.originalPosition;
        const targetZ = originalPosition.z + targetZOffset;
        
        // 如果要抽出设备，先收回当前机柜中的其他已抽出设备，等待完成后再执行新的移动
        if (!isExtended) {
            console.log(`🔄 抽出新设备前，先收回其他设备`);
            // 抽出设备时清除悬浮高亮
            clearCabinetHighlight();
            console.log(`✨ 清除悬浮高亮效果 - 设备抽出`);
            
            retractAllDevicesInCabinet(selectedCabinetKey.value, deviceToMove).then(() => {
                console.log(`🔄 其他设备收回完成，开始抽出目标设备`);
                console.log(`📏 设备移动详情:`, {
                    设备名称: deviceToMove.name,
                    当前位置: deviceToMove.position,
                    原始位置: originalPosition,
                    目标Z值: targetZ,
                    移动距离: targetZ - deviceToMove.position.z
                });
                
                // 其他设备收回完成后，执行新设备的抽出动画
                animateDeviceMovement(deviceToMove, targetZ, '抽出').then(() => {
                    console.log(`✅ 设备抽出动画完成:`, {
                        设备名称: deviceToMove.name,
                        最终位置: deviceToMove.position
                    });
                    
                    // 更新状态到设备整体对象上
                    deviceToMove.userData.isExtended = true;
                    
                    // 暂时注释掉相机移动，先确保设备移动正常
                    console.log(`🎬 设备抽出完成，相机移动功能暂时禁用`);
                    // moveToDeviceView(deviceToMove, cabinetName, 1200).then(() => {
                    //     console.log(`✅ 相机已移动到设备 ${deviceToMove.name} 面前`);
                    // }).catch((error) => {
                    //     console.error(`❌ 移动相机到设备面前失败:`, error);
                    // });
                });
            });
        } else {
            // 收回设备，直接执行动画
            console.log(`🔄 收回设备`);
            // 收回设备时清除悬浮高亮
            clearCabinetHighlight();
            console.log(`✨ 清除悬浮高亮效果 - 设备收回`);
            
            animateDeviceMovement(deviceToMove, targetZ, '收回').then(() => {
                // 更新状态到设备整体对象上
                deviceToMove.userData.isExtended = false;
                
                // 设备收回完成后，相机返回到机柜视角
                console.log(`🎬 设备收回完成，相机返回机柜视角: ${cabinetName}`);
                const cabinetView = cabinetViewPositions[cabinetName];
                if (cabinetView) {
                    smoothMoveCamera(cabinetView, 1000).then(() => {
                        console.log(`✅ 相机已返回机柜 ${cabinetName} 视角`);
                    }).catch((error) => {
                        console.error(`❌ 返回机柜视角失败:`, error);
                    });
                }
            });
        }
        
    } else {
        console.warn(`⚠️ 未找到设备对象: ${fullDeviceId}`);
        console.log('📋 模型中的所有对象名称（前100个）:');
        
        // 调试：列出模型中的对象名称，帮助定位问题
        let allObjects = [];
        model.traverse((child) => {
            if (child.name) {
                allObjects.push(child.name);
            }
        });
        
        console.log('🔍 模型中的对象总数:', allObjects.length);
        console.log('🔍 前100个对象名称:', allObjects.slice(0, 100));
        
        // 查找包含相关关键字的对象
        const relatedObjects = allObjects.filter(name => {
            const nameLower = name.toLowerCase();
            return nameLower.includes('sb') || 
                   nameLower.includes(cabinetKey.toLowerCase()) ||
                   nameLower.includes(String(fullDeviceId).toLowerCase());
        });
        
        console.log('🔍 找到相关的对象名称:', relatedObjects);
        console.log(`💡 提示：请检查设备ID "${fullDeviceId}" 在3D模型中的命名格式`);
    }
}



/**
 * 通过设备ID搜索所在机柜
 * @param {string} deviceId - 设备构件名
 * @returns {string|null} 机柜键值或null
 */
function findCabinetByDeviceId(deviceId) {
    if (!model || !deviceId) {
        return null;
    }
    
    console.log('🔍 搜索设备所在机柜:', deviceId);
    
    // 遍历模型查找设备
    let foundCabinet = null;
    
    model.traverse((child) => {
        if (child.name === deviceId) {
            console.log('✅ 找到设备对象:', child.name);
            
            // 向上遍历父级对象，查找机柜
            let parent = child.parent;
            while (parent && parent !== scene) {
                if (parent.name && parent.name.includes('Cabinet')) {
                    // 从机柜名称中提取机柜编号
                    const cabinetMatch = parent.name.match(/([AB]\d+)/i);
                    if (cabinetMatch) {
                        const cabinetNumber = cabinetMatch[1].toUpperCase();
                        foundCabinet = getCabinetViewKey(cabinetNumber);
                        console.log('✅ 找到设备所在机柜:', foundCabinet);
                        break;
                    }
                }
                parent = parent.parent;
            }
            
            // 如果通过父级找不到，尝试通过位置推断
            if (!foundCabinet) {
                foundCabinet = findCabinetByPosition(child.position);
            }
            
            return foundCabinet;
        }
    });
    
    if (!foundCabinet) {
        console.warn('⚠️ 未找到设备所在机柜:', deviceId);
        // 尝试默认机柜
        foundCabinet = 'A1';
    }
    
    return foundCabinet;
}

/**
 * 通过位置推断机柜
 * @param {THREE.Vector3} position - 设备位置
 * @returns {string|null} 机柜键值或null
 */
function findCabinetByPosition(position) {
    let closestCabinet = null;
    let minDistance = Infinity;
    
    // 遍历所有机柜视角配置，找到最近的机柜
    Object.keys(cabinetViewPositions).forEach(cabinetKey => {
        const cabinetView = cabinetViewPositions[cabinetKey];
        const distance = position.distanceTo(new THREE.Vector3(
            cabinetView.position.x,
            cabinetView.position.y,
            cabinetView.position.z
        ));
        
        if (distance < minDistance) {
            minDistance = distance;
            closestCabinet = cabinetKey;
        }
    });
    
    console.log('📍 通过位置推断机柜:', closestCabinet, '距离:', minDistance.toFixed(2));
    return closestCabinet;
}

/**
 * 解析设备ID，提取机柜编号和设备编号
 * @param {string} deviceId - 设备ID，格式如 "A1-SB001"
 * @returns {Object} 解析结果包含机柜编号和设备编号
 */
function parseDeviceId(deviceId) {
    if (!deviceId || typeof deviceId !== 'string') {
        console.warn('⚠️ 无效的设备ID:', deviceId);
        return { cabinetNumber: null, deviceNumber: null, isValid: false };
    }

    // 主要格式: A1-SB001 (机柜编号-设备编号)
    const pattern = /^([AB]\d+)-(.+)$/i;
    const match = deviceId.match(pattern);
    
    if (match) {
        const cabinetNumber = match[1].toUpperCase(); // A1, B2 等
        const deviceNumber = match[2]; // SB001 等
        
        console.log('✅ 设备ID解析成功:', {
            原始ID: deviceId,
            机柜编号: cabinetNumber,
            设备编号: deviceNumber
        });
        
        return {
            cabinetNumber,
            deviceNumber,
            isValid: true,
            originalId: deviceId
        };
    }

    console.warn('⚠️ 无法解析设备ID格式，期望格式为 A1-SB001:', deviceId);
    return { 
        cabinetNumber: null, 
        deviceNumber: null, 
        isValid: false, 
        originalId: deviceId 
    };
}







/**
 * 打开机柜门
 * @param {THREE.Object3D} cabinetObject - 机柜对象
 * @param {string} cabinetNumber - 机柜编号
 */
async function openCabinetDoor(cabinetObject, cabinetNumber) {
    return new Promise((resolve) => {
        const doorObject = cabinetObject.userData.door;
        
        if (!doorObject) {
            console.warn(`⚠️ 机柜 ${cabinetNumber} 没有找到门对象，跳过开门动画`);
            resolve();
            return;
        }

        console.log(`🚪 开始打开机柜 ${cabinetNumber} 的门:`, doorObject.name);

        // 记录门的初始状态
        if (!doorObject.userData.originalRotation) {
            doorObject.userData.originalRotation = doorObject.rotation.clone();
        }

        // 计算门的打开角度（向右打开90度）
        const targetRotation = doorObject.userData.originalRotation.clone();
        targetRotation.y += Math.PI / 2; // 90度

        // 执行开门动画
        const startTime = performance.now();
        const duration = 1500; // 1.5秒

        function animateDoor(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easeProgress = easeInOutCubic(progress);

            // 插值计算门的旋转角度
            doorObject.rotation.y = THREE.MathUtils.lerp(
                doorObject.userData.originalRotation.y,
                targetRotation.y,
                easeProgress
            );

            if (progress < 1) {
                requestAnimationFrame(animateDoor);
            } else {
                console.log(`✅ 机柜 ${cabinetNumber} 门已打开`);
                doorObject.userData.isOpen = true;
                resolve();
            }
        }

        requestAnimationFrame(animateDoor);
    });
}

/**
 * 抽出指定设备
 * @param {THREE.Object3D} cabinetObject - 机柜对象
 * @param {string} deviceNumber - 设备编号
 * @param {string} fullDeviceId - 完整设备ID
 */
async function extractDevice(cabinetObject, deviceNumber, fullDeviceId) {
    return new Promise((resolve) => {
        // 在机柜内查找设备对象
        let deviceObject = null;
        
        // 遍历机柜的子对象查找设备
        cabinetObject.traverse((child) => {
            if (!child.name) return;
            
            const childName = child.name.toLowerCase();
            const targetDevice = deviceNumber.toLowerCase();
            
            // 多种设备匹配模式
            const devicePatterns = [
                new RegExp(`${targetDevice}`, 'i'),           // SB001
                new RegExp(`${fullDeviceId}`, 'i'),           // A1-SB001
                new RegExp(`device.*${targetDevice}`, 'i'),   // device_SB001
                new RegExp(`${targetDevice}.*device`, 'i')    // SB001_device
            ];

            for (const pattern of devicePatterns) {
                if (pattern.test(childName)) {
                    deviceObject = child;
                    console.log(`📦 找到设备对象:`, { 
                        搜索目标: deviceNumber, 
                        找到对象: child.name 
                    });
                    break;
                }
            }
        });

        if (!deviceObject) {
            console.warn(`⚠️ 在机柜中未找到设备: ${deviceNumber}，创建虚拟设备进行演示`);
            
            // 创建一个虚拟设备用于演示
            const deviceGeometry = new THREE.BoxGeometry(0.4, 0.08, 0.3);
            const deviceMaterial = new THREE.MeshStandardMaterial({ 
                color: 0x4CAF50,
                emissive: 0x1B5E20,
                emissiveIntensity: 0.2
            });
            deviceObject = new THREE.Mesh(deviceGeometry, deviceMaterial);
            
            // 将虚拟设备放置在机柜内
            const cabinetBox = new THREE.Box3().setFromObject(cabinetObject);
            const cabinetCenter = cabinetBox.getCenter(new THREE.Vector3());
            
            deviceObject.position.set(
                cabinetCenter.x + 0.2,
                cabinetCenter.y,
                cabinetCenter.z
            );
            
            deviceObject.name = `虚拟设备_${deviceNumber}`;
            deviceObject.userData = { 
                type: 'virtual-device', 
                deviceId: fullDeviceId,
                isVirtual: true 
            };
            
            // 添加到场景
            scene.add(deviceObject);
            console.log('📦 创建了虚拟设备用于演示');
        }

        // 记录设备的初始位置
        if (!deviceObject.userData.originalPosition) {
            deviceObject.userData.originalPosition = deviceObject.position.clone();
        }

        console.log(`📤 开始抽出设备: ${deviceNumber}`);

        // 计算设备抽出的目标位置（向前抽出）
        const extractDistance = 0.8; // 抽出距离
        const targetPosition = deviceObject.userData.originalPosition.clone();
        targetPosition.x += extractDistance; // 向X轴正方向抽出

        // 执行设备抽出动画
        const startTime = performance.now();
        const duration = 2000; // 2秒

        function animateExtraction(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easeProgress = easeInOutCubic(progress);

            // 插值计算设备位置
            deviceObject.position.lerpVectors(
                deviceObject.userData.originalPosition,
                targetPosition,
                easeProgress
            );

            // 添加轻微的发光效果
            if (deviceObject.material && deviceObject.material.emissiveIntensity !== undefined) {
                deviceObject.material.emissiveIntensity = 0.2 + 0.3 * Math.sin(elapsed * 0.01);
            }

            if (progress < 1) {
                requestAnimationFrame(animateExtraction);
            } else {
                console.log(`✅ 设备 ${deviceNumber} 已成功抽出`);
                deviceObject.userData.isExtracted = true;
                
                // 添加设备信息标签
                addDeviceLabel(deviceObject, fullDeviceId);
                
                resolve();
            }
        }

        requestAnimationFrame(animateExtraction);
    });
}

/**
 * 为抽出的设备添加信息标签
 * @param {THREE.Object3D} deviceObject - 设备对象
 * @param {string} deviceId - 设备ID
 */
function addDeviceLabel(deviceObject, deviceId) {
    // 创建设备标签HTML
    const labelHTML = `
        <div style="
            background: linear-gradient(135deg, rgba(39, 171, 255, 0.9), rgba(65, 239, 255, 0.8));
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            pointer-events: none;
            white-space: nowrap;
        ">
            📦 ${deviceId}
        </div>
    `;

    const labelDiv = document.createElement('div');
    labelDiv.innerHTML = labelHTML;

    // 创建CSS2D标签对象
    const deviceLabel = new CSS2DObject(labelDiv);
    deviceLabel.position.set(0, 0.2, 0); // 设备上方
    deviceLabel.userData = { 
        type: 'device-label', 
        deviceId: deviceId 
    };

    // 将标签添加到设备对象
    deviceObject.add(deviceLabel);

    console.log(`🏷️ 已为设备 ${deviceId} 添加信息标签`);
}

/**
 * 测试机柜设备操作序列的函数 - 基于现有方法
 * @param {string} deviceId - 设备ID，默认为 "A1-SB001"
 */
function testCabinetDeviceSequence(deviceId = "A1-SB001") {
    console.log('🧪 开始测试机柜设备操作序列（基于现有方法）:', deviceId);
    
    // 模拟设备选择事件
    const testEvent = new CustomEvent('deviceSelected', {
        detail: {
            deviceId: deviceId,
            deviceName: `测试设备_${deviceId}`,
            originalDeviceId: deviceId,
            cabinetName: deviceId.split('-')[0] + '柜'
        }
    });
    
    // 直接调用基于现有方法的事件处理函数
    handleDeviceSelected(testEvent);
    
    console.log('🧪 测试事件已发送，使用现有的方法执行操作');
    console.log('📝 执行流程（基于现有方法）:');
    console.log('  1. 使用现有的 smoothMoveCamera 方法定位机柜视角');
    console.log('  2. 使用现有的 handleCabinetDoor 方法打开机柜门');
    console.log('  3. 使用现有的 handleCabinetDeviceClick 和 animateDeviceMovement 方法抽出设备');
    console.log('💡 其他测试示例:');
    console.log('  - testCabinetDeviceSequence("A2-SB002")');
    console.log('  - testCabinetDeviceSequence("B1-SW001")');
    console.log('  - testCabinetDeviceSequence("A3")  // 只定位机柜，不抽出设备');
}

// 相机定位到指定设备（保留原有功能作为备用）
function focusOnDevice(deviceId) {
    if (!model || !camera || !controls) {
        return;
    }

    let targetObject = null;

    // 遍历模型查找目标设备
    model.traverse((child) => {
        if (child.name === deviceId) {
            targetObject = child;
        }
    });

    if (targetObject) {
        // 计算目标对象的边界盒
        const box = new THREE.Box3().setFromObject(targetObject);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        console.log(`找到设备 ${deviceId}:`, {
            center: center,
            size: size
        });

        // 计算合适的相机位置
        const distance = Math.max(size.x, size.y, size.z) * 1; // 根据设备大小调整距离
        const cameraPosition = new THREE.Vector3(
            center.x + distance * 0.7,
            center.y + distance * 0.8,
            center.z + distance * 0.7
        );

        // 平滑移动相机
        animateCameraToPosition(cameraPosition, center, 1500);

    } else {
        console.warn(`未找到设备: ${deviceId}`);
    }
}

/**
 * 平滑移动相机到指定位置
 * @param {THREE.Vector3} targetPosition - 目标位置
 * @param {THREE.Vector3} targetLookAt - 观察目标点
 * @param {number} duration - 动画持续时间（毫秒）
 * @returns {Promise} 动画完成的Promise
 */
function animateCameraToPosition(targetPosition, targetLookAt, duration = 1000) {
    return new Promise((resolve) => {
        const startPosition = camera.position.clone();
        const startLookAt = controls.target.clone();
        const startTime = performance.now();

        function animate(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数实现平滑移动
            const easeProgress = easeInOutCubic(progress);

            // 插值计算相机位置
            camera.position.lerpVectors(startPosition, targetPosition, easeProgress);

            // 插值计算控制器目标位置
            controls.target.lerpVectors(startLookAt, targetLookAt, easeProgress);

            // 更新控制器
            controls.update();

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                console.log('相机定位完成');
                resolve();
            }
        }

        requestAnimationFrame(animate);
    });
}

// 添加热力图功能
function addHeatmap() {
    if (!model) {
        console.warn('模型未加载，无法创建热力图');
        return;
    }

    // 计算模型边界盒
    const box = new THREE.Box3().setFromObject(model);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    console.log('模型信息:', {
        center: center,
        size: size,
        min: box.min,
        max: box.max
    });

    // 创建热力图Canvas
    const canvas = document.createElement('canvas');
    const canvasSize = 512;
    canvas.width = canvasSize;
    canvas.height = canvasSize;
    const ctx = canvas.getContext('2d');

    // 生成热力图数据点
    const heatmapData = generateHeatmapData();

    // 绘制热力图
    drawHeatmap(ctx, heatmapData, canvasSize);

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 创建热力图平面几何体
    const planeGeometry = new THREE.PlaneGeometry(size.x, size.z);

    // 创建热力图材质
    const planeMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        opacity: 0.7,
        alphaTest: 0.1
    });

    // 创建热力图平面
    const heatmapPlane = new THREE.Mesh(planeGeometry, planeMaterial);

    // 设置热力图位置（在模型上方）
    heatmapPlane.position.set(center.x, box.max.y + 0.1, center.z);
    heatmapPlane.rotation.x = -Math.PI / 2; // 旋转90度使其水平

    // 设置用户数据标识
    heatmapPlane.userData = { type: 'heatmap' };

    // 添加到场景
    scene.add(heatmapPlane);

    console.log('热力图已创建并添加到场景');

    // 将热力图相关对象添加到调试对象
    if (window.threeDebug) {
        window.threeDebug.heatmap = heatmapPlane;
        window.threeDebug.heatmapCanvas = canvas;

        // 更新热力图数据的方法
        window.threeDebug.updateHeatmap = (newData) => {
            if (newData) {
                drawHeatmap(ctx, newData, canvasSize);
                texture.needsUpdate = true;
                console.log('热力图数据已更新');
            }
        };

        // 切换热力图显示/隐藏
        window.threeDebug.toggleHeatmap = () => {
            heatmapPlane.visible = !heatmapPlane.visible;
            console.log('热力图可见性:', heatmapPlane.visible);
        };

        // 设置热力图透明度
        window.threeDebug.setHeatmapOpacity = (opacity) => {
            planeMaterial.opacity = Math.max(0, Math.min(1, opacity));
            console.log('热力图透明度设置为:', planeMaterial.opacity);
        };

        // 移除热力图
        window.threeDebug.removeHeatmap = () => {
            scene.remove(heatmapPlane);
            planeGeometry.dispose();
            planeMaterial.dispose();
            texture.dispose();

            delete window.threeDebug.heatmap;
            delete window.threeDebug.heatmapCanvas;
            delete window.threeDebug.updateHeatmap;
            delete window.threeDebug.toggleHeatmap;
            delete window.threeDebug.setHeatmapOpacity;
            delete window.threeDebug.removeHeatmap;

            console.log('热力图已移除');
        };
    }

    return heatmapPlane;
}

// 生成热力图数据
function generateHeatmapData() {
    // 模拟热力图数据点
    const data = [];
    const numPoints = 20;

    for (let i = 0; i < numPoints; i++) {
        data.push({
            x: Math.random(), // 0-1 范围内的x坐标
            y: Math.random(), // 0-1 范围内的y坐标
            intensity: Math.random() * 0.8 + 0.2, // 0.2-1.0 范围内的强度
            radius: 0.15 + Math.random() * 0.1 // 0.15-0.25 范围内的半径
        });
    }

    // 添加一些固定的高温点（模拟机柜位置）
    data.push(
        { x: 0.2, y: 0.3, intensity: 0.9, radius: 0.2 },
        { x: 0.8, y: 0.3, intensity: 0.8, radius: 0.18 },
        { x: 0.2, y: 0.7, intensity: 0.85, radius: 0.19 },
        { x: 0.8, y: 0.7, intensity: 0.75, radius: 0.17 }
    );

    return data;
}

// 绘制热力图
function drawHeatmap(ctx, data, canvasSize) {
    // 清空canvas
    ctx.clearRect(0, 0, canvasSize, canvasSize);

    // 为每个数据点创建径向渐变
    data.forEach(point => {
        const x = point.x * canvasSize;
        const y = point.y * canvasSize;
        const radius = point.radius * canvasSize;

        // 创建径向渐变
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);

        // 根据强度确定颜色
        const color = getHeatmapColor(point.intensity);

        gradient.addColorStop(0, `rgba(${color.r}, ${color.g}, ${color.b}, ${point.intensity})`);
        gradient.addColorStop(0.6, `rgba(${color.r}, ${color.g}, ${color.b}, ${point.intensity * 0.3})`);
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        // 设置混合模式以实现热力图效果
        ctx.globalCompositeOperation = 'screen';

        // 绘制圆形
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
    });

    // 重置混合模式
    ctx.globalCompositeOperation = 'source-over';
}

// 根据强度获取热力图颜色
function getHeatmapColor(intensity) {
    // 使用经典的热力图颜色映射：蓝色->绿色->黄色->红色
    const colors = [
        { r: 0, g: 0, b: 255 },     // 蓝色 (低温)
        { r: 0, g: 255, b: 255 },   // 青色
        { r: 0, g: 255, b: 0 },     // 绿色
        { r: 255, g: 255, b: 0 },   // 黄色
        { r: 255, g: 0, b: 0 }      // 红色 (高温)
    ];

    // 将强度映射到颜色数组索引
    const scaledIntensity = intensity * (colors.length - 1);
    const colorIndex = Math.floor(scaledIntensity);
    const remainder = scaledIntensity - colorIndex;

    // 如果是最后一个颜色，直接返回
    if (colorIndex >= colors.length - 1) {
        return colors[colors.length - 1];
    }

    // 在两个颜色之间进行插值
    const color1 = colors[colorIndex];
    const color2 = colors[colorIndex + 1];

    return {
        r: Math.round(color1.r + (color2.r - color1.r) * remainder),
        g: Math.round(color1.g + (color2.g - color1.g) * remainder),
        b: Math.round(color1.b + (color2.b - color1.b) * remainder)
    };
}

// 初始化视频播放器
const initVideoPlayer = async (cameraName) => {
    try {
        isVideoLoading.value = true;
        videoPlayerUrl.value = ''; // 开始加载前清空旧地址
        await nextTick();

        // 获取摄像头URL
        const apiUrl = cameraUrlMap[cameraName];
        if (!apiUrl) {
            throw new Error(`未找到摄像头'${cameraName}'的URL`);
        }
        
        console.log('正在获取视频流URL:', apiUrl);

        const response = await fetch(apiUrl, {
            headers: {
                'tenant-id': '1'
            }
        });

        if (!response.ok) {
            throw new Error(`请求视频流URL失败: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (result.code !== 0) {
            throw new Error(result.msg || '获取视频流URL时发生错误');
        }

        const videoUrl = result.data;
        console.log('视频URL:', videoUrl);
        videoPlayerUrl.value = videoUrl;

    } catch (error) {
        console.error('初始化视频播放器失败:', error);
    } finally {
        isVideoLoading.value = false;
    }
};

const closeVideoModal = () => {
    showVideoModal.value = false;
    videoPlayerUrl.value = '';
};

</script>

<style scoped lang="scss">
.three-scene-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#map {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: all;
    background: url('@/assets/images/modalBg.png') no-repeat center center;
    background-size: cover;
}

/* 门禁列表样式 */
.menjin-list {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 852px;
    height: 513px;
    background: #14354E;
    border-radius: 4px;
    z-index: 9999;
    overflow: hidden;
}

.menjin-header {
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;

    .menjin-title {
        margin-left: 55px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 22px;
    }

    .close-btn {
        width: 16px;
        height: 16px;
        cursor: pointer;
        margin-right: 26px;
    }
}



.menjin-table {
    height: calc(100% - 54px);
    overflow: hidden;
}

.device-detail {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 852px;
    // height: 513px;
    background: #14354E;
    border-radius: 4px;
    z-index: 9999;

    .detail-title {
        padding: 16px 46px;
        box-sizing: border-box;
        font-weight: 500;
        font-size: 14px;
        color: #7BCCFF;
    }

    .device-info {
        margin: 0 16px;
        padding-bottom: 20px;
    }

    .info-row {
        display: flex;
        margin-bottom: 11px;
        border: 1px solid rgba(151, 151, 151, 0.1);
        height: 51px;
        align-items: center;
    }

    .info-item {
        width: 33.33%;
        display: flex;
        align-items: center;
    }

    .info-block {
        width: 6px;
        height: 4px;
        background: #27ABFF;
        margin-right: 16px;
        margin-left: 34px;
    }

    .info-label {
        font-weight: 400;
        font-size: 16px;
        color: #A9C5C8;
        margin-right: 6px;
    }

    .info-value {
        font-weight: 400;
        font-size: 16px;
    }

    &.loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100px;

        .loading-text {
            font-weight: 400;
            font-size: 16px;
            color: #A9C5C8;
        }
    }
}

.table-header {
    display: grid;
    grid-template-columns: 180px 120px 120px 160px 120px 120px;
    padding: 20px 56px;
    box-sizing: border-box;
}

.th {
    font-weight: 500;
    font-size: 14px;
    color: #7BCCFF;
    // text-align: center;
}

.table-body {
    height: calc(100% - 45px);
    overflow-y: auto;
    padding: 0 24px;
}

.tr {
    display: grid;
    grid-template-columns: 180px 120px 120px 160px 120px 120px;
    height: 51px;
    line-height: 51px;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid rgba(#979797, 0.1);
    padding: 0 30px;
    box-sizing: border-box;
}

.tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.td {
    font-weight: 400;
    font-size: 16px;
    // text-align: center;
    display: flex;
    align-items: center;
    // justify-content: center;
    opacity: 0.9;
}

.status {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-normal {
    background-color: #40AAFC;
}

.status-reject {
    background-color: #FD8D79;
}

/* 滚动条样式 */
.table-body::-webkit-scrollbar {
    width: 6px;
}

.table-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 存储标签样式 */
.storage-label {
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.storage-text {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 视频弹窗样式 */
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.video-container {
    width: 800px;
    background: #14354E;
    border-radius: 4px;
    overflow: hidden;
}

.video-player {
    position: relative;
    height: 500px;
    width: 100%;
    background-color: #000;
}

.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-text {
    color: #ffffff;
    font-size: 18px;
}

/* 悬浮提示框样式 */
.hover-tooltip {
    position: fixed;
    z-index: 10001;
    background: url('@/assets/images/tooltipBg.png') no-repeat center;
    background-size: 100% 100%;
    color: rgba(255, 255, 255, 0.85);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    pointer-events: none;
    white-space: nowrap;
    transition: opacity 0.2s ease-in-out;
}
</style>

<style>
/* 温湿度显示面板样式 */
.temperature-humidity-display {
    background: rgba(21, 52, 84, 0.75);
    padding: 16px 16px;
    min-width: 110px;
    pointer-events: none;
    backdrop-filter: blur(10px);
    border-radius: 6px;
}

.display-header {
    font-size: 14px;
    font-weight: bold;
    color: #7BCCFF;
    margin-bottom: 8px;
    padding-bottom: 4px;
}

.display-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.display-row:last-child {
    margin-bottom: 0;
}

.display-label {
    font-size: 13px;
    color: #A9C5C8;
    font-weight: 400;
}

.display-value {
    font-size: 18px;
    font-weight: 800;
    font-family: DIN Alternate;
    /* text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); */
}

.display-value.temperature {
    color: #FF6B6B;
}

.display-value.humidity {
    color: #1db5d4;
}
</style>
