import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from '@components/layout.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: Layout,
    children: [
      {
        path: '/',
        name: 'overview',
        component: () => import('@views/overview.vue')
      },
      {
        path: '/alarm',
        name: 'alarm',
        component: () => import('@views/alarm.vue')
      },
      {
        path: '/device',
        name: 'device',
        component: () => import('@views/device.vue')
      },
      {
        path: '/network',
        name: 'network',
        component: () => import('@views/network.vue')
      },
    ]
  },
  // 404 错误处理路由
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router 