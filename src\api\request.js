/**
 * API 请求配置
 * 基于 fetch 的简单封装，也可以换成 axios
 */

// API 基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (config) => {
  // 添加认证头
  const token = localStorage.getItem('token')
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`,
      'tenant-id': 1
    }
  }
  return config
}

// 响应拦截器
const responseInterceptor = async (response) => {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const data = await response.json()

  // 根据业务逻辑处理响应
  if (data.code !== 0) {
    throw new Error(data.message || '请求失败')
  }

  return data.data
}

// 基础请求方法
const request = async (url, options = {}) => {
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options
  }

  // 应用请求拦截器
  const interceptedConfig = requestInterceptor(config)

  // 完整的 URL
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT)

    const response = await fetch(fullUrl, {
      ...interceptedConfig,
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    return await responseInterceptor(response)
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  }
}

// GET 请求
export const get = (url, params = {}) => {
  const searchParams = new URLSearchParams(params)
  const queryString = searchParams.toString()
  const finalUrl = queryString ? `${url}?${queryString}` : url

  return request(finalUrl)
}

// POST 请求
export const post = (url, data = {}) => {
  return request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  })
}

// PUT 请求
export const put = (url, data = {}) => {
  return request(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  })
}

// DELETE 请求
export const del = (url) => {
  return request(url, {
    method: 'DELETE'
  })
}

// 文件上传
export const upload = (url, formData) => {
  return request(url, {
    method: 'POST',
    body: formData,
    headers: {} // 让浏览器自动设置 Content-Type
  })
}

export default {
  get,
  post,
  put,
  delete: del,
  upload
} 