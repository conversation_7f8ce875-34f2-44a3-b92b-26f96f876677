import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export class ThreeService {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.models = new Map();
        this.lights = new Map();
        this.animationFrameId = null;
        this.isAnimating = false;
        
        // 加载器
        this.gltfLoader = new GLTFLoader();
        this.textureLoader = new THREE.TextureLoader();
        
        // 配置
        this.config = {
            scene: {
                background: 0xffffff,
                fog: null
            },
            camera: {
                fov: 75,
                near: 0.1,
                far: 1000,
                position: { x: 0, y: 10, z: 20 }
            },
            renderer: {
                antialias: true,
                alpha: true,
                shadowMap: true,
                shadowMapType: THREE.PCFSoftShadowMap
            },
            controls: {
                enableDamping: true,
                dampingFactor: 0.05,
                enableZoom: true,
                enableRotate: true,
                enablePan: true,
                maxDistance: 100,
                minDistance: 5,
                maxPolarAngle: Math.PI / 2
            }
        };
    }

    /**
     * 初始化Three.js场景
     * @param {string} containerId - 容器DOM元素ID
     * @param {Object} options - 可选配置
     */
    init(containerId, options = {}) {
        try {
            // 合并配置
            this.config = { ...this.config, ...options };
            
            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`容器元素 ${containerId} 不存在`);
            }

            this.initScene();
            this.initCamera(container);
            this.initRenderer(container);
            this.initControls();
            this.initLights();
            this.initGround();
            this.bindEvents();

            console.log('ThreeService 初始化成功');
            return true;
        } catch (error) {
            console.error('ThreeService 初始化失败:', error);
            return false;
        }
    }

    /**
     * 初始化场景
     */
    initScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(this.config.scene.background);
        
        if (this.config.scene.fog) {
            this.scene.fog = new THREE.Fog(
                this.config.scene.fog.color,
                this.config.scene.fog.near,
                this.config.scene.fog.far
            );
        }
    }

    /**
     * 初始化相机
     */
    initCamera(container) {
        this.camera = new THREE.PerspectiveCamera(
            this.config.camera.fov,
            container.clientWidth / container.clientHeight,
            this.config.camera.near,
            this.config.camera.far
        );
        
        const pos = this.config.camera.position;
        this.camera.position.set(pos.x, pos.y, pos.z);
    }

    /**
     * 初始化渲染器
     */
    initRenderer(container) {
        this.renderer = new THREE.WebGLRenderer({
            antialias: this.config.renderer.antialias,
            alpha: this.config.renderer.alpha
        });
        
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        
        if (this.config.renderer.shadowMap) {
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = this.config.renderer.shadowMapType;
        }
        
        container.appendChild(this.renderer.domElement);
    }

    /**
     * 初始化控制器
     */
    initControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        
        const config = this.config.controls;
        this.controls.enableDamping = config.enableDamping;
        this.controls.dampingFactor = config.dampingFactor;
        this.controls.enableZoom = config.enableZoom;
        this.controls.enableRotate = config.enableRotate;
        this.controls.enablePan = config.enablePan;
        this.controls.maxDistance = config.maxDistance;
        this.controls.minDistance = config.minDistance;
        this.controls.maxPolarAngle = config.maxPolarAngle;
    }

    /**
     * 初始化光照
     */
    initLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        this.lights.set('ambient', ambientLight);

        // 定向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        this.scene.add(directionalLight);
        this.lights.set('directional', directionalLight);

        // 点光源
        const pointLight = new THREE.PointLight(0x00f0ff, 0.8, 100);
        pointLight.position.set(0, 20, 0);
        this.scene.add(pointLight);
        this.lights.set('point', pointLight);
    }

    /**
     * 初始化地面
     */
    initGround() {
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x333333,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        this.models.set('ground', ground);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        window.addEventListener('resize', this.onWindowResize.bind(this));
    }

    /**
     * 加载GLB模型
     * @param {string} url - 模型URL
     * @param {Object} options - 加载选项
     * @returns {Promise}
     */
    loadGLBModel(url, options = {}) {
        return new Promise((resolve, reject) => {
            console.log(`开始加载GLB模型: ${url}`);
            console.log('加载选项:', options);
            
            this.gltfLoader.load(
                url,
                (gltf) => {
                    console.log('GLTF文件加载成功:', gltf);
                    const model = gltf.scene;
                    
                    if (!model) {
                        console.error('模型场景为空');
                        reject(new Error('模型场景为空'));
                        return;
                    }
                    
                    console.log('模型场景:', model);
                    console.log('模型子对象数量:', model.children.length);
                    
                    // 应用配置
                    if (options.scale) {
                        model.scale.set(options.scale, options.scale, options.scale);
                        console.log(`设置模型缩放: ${options.scale}`);
                    }
                    
                    if (options.position) {
                        model.position.set(options.position.x, options.position.y, options.position.z);
                        console.log(`设置模型位置:`, options.position);
                    }
                    
                    if (options.rotation) {
                        model.rotation.set(options.rotation.x, options.rotation.y, options.rotation.z);
                        console.log(`设置模型旋转:`, options.rotation);
                    }

                    // 启用阴影
                    if (options.enableShadow !== false) {
                        let meshCount = 0;
                        model.traverse((child) => {
                            if (child.isMesh) {
                                meshCount++;
                                child.castShadow = true;
                                child.receiveShadow = true;
                                
                                // 材质增强
                                if (child.material && options.materialEnhance !== false) {
                                    child.material.metalness = options.metalness || 0.3;
                                    child.material.roughness = options.roughness || 0.7;
                                }
                            }
                        });
                        console.log(`处理了 ${meshCount} 个网格对象`);
                    }

                    // 添加到场景
                    this.scene.add(model);
                    console.log('模型已添加到场景');
                    
                    // 保存模型引用
                    const modelName = options.name || 'model_' + Date.now();
                    this.models.set(modelName, model);
                    
                    // 计算模型边界盒
                    const box = new THREE.Box3().setFromObject(model);
                    const size = box.getSize(new THREE.Vector3());
                    const center = box.getCenter(new THREE.Vector3());
                    console.log('模型边界盒尺寸:', size);
                    console.log('模型中心位置:', center);

                    // 自动调整相机
                    if (options.autoFitCamera !== false) {
                        this.fitCameraToModel(model);
                    }

                    console.log(`模型 ${modelName} 加载成功`);
                    resolve({ model, name: modelName, size, center });
                },
                (progress) => {
                    if (progress.total > 0) {
                        const percent = (progress.loaded / progress.total * 100).toFixed(2);
                        console.log(`模型加载进度: ${percent}%`);
                        
                        if (options.onProgress) {
                            options.onProgress(percent);
                        }
                    }
                },
                (error) => {
                    console.error('模型加载失败 - 错误详情:', error);
                    console.error('加载的URL:', url);
                    console.error('错误类型:', error.constructor.name);
                    console.error('错误消息:', error.message);
                    reject(error);
                }
            );
        });
    }

    /**
     * 调整相机以适应模型
     */
    fitCameraToModel(model) {
        const box = new THREE.Box3().setFromObject(model);
        const size = box.getSize(new THREE.Vector3()).length();
        const center = box.getCenter(new THREE.Vector3());

        this.camera.position.copy(center);
        this.camera.position.x += size / 2.0;
        this.camera.position.y += size / 5.0;
        this.camera.position.z += size / 2.0;
        this.camera.lookAt(center);

        this.controls.target.copy(center);
        this.controls.update();
    }

    /**
     * 创建基础几何体
     */
    createCube(options = {}) {
        const geometry = new THREE.BoxGeometry(
            options.width || 1,
            options.height || 1,
            options.depth || 1
        );
        
        const material = new THREE.MeshPhongMaterial({
            color: options.color || 0x00f0ff,
            transparent: options.transparent || false,
            opacity: options.opacity || 1
        });

        const cube = new THREE.Mesh(geometry, material);
        
        if (options.position) {
            cube.position.set(options.position.x, options.position.y, options.position.z);
        }
        
        if (options.rotation) {
            cube.rotation.set(options.rotation.x, options.rotation.y, options.rotation.z);
        }

        cube.castShadow = options.castShadow !== false;
        cube.receiveShadow = options.receiveShadow !== false;

        this.scene.add(cube);
        
        const name = options.name || 'cube_' + Date.now();
        this.models.set(name, cube);
        
        return cube;
    }

    /**
     * 开始动画循环
     */
    startAnimation() {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        this.animate();
    }

    /**
     * 停止动画循环
     */
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
    }

    /**
     * 动画循环
     */
    animate() {
        if (!this.isAnimating) return;
        
        this.animationFrameId = requestAnimationFrame(() => this.animate());
        
        // 更新控制器
        if (this.controls) {
            this.controls.update();
        }
        
        // 渲染场景
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    /**
     * 窗口大小变化处理
     */
    onWindowResize() {
        if (!this.camera || !this.renderer) return;
        
        const container = this.renderer.domElement.parentElement;
        if (container) {
            this.camera.aspect = container.clientWidth / container.clientHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(container.clientWidth, container.clientHeight);
        }
    }

    /**
     * 获取模型
     */
    getModel(name) {
        return this.models.get(name);
    }

    /**
     * 获取光源
     */
    getLight(name) {
        return this.lights.get(name);
    }

    /**
     * 销毁场景
     */
    dispose() {
        this.stopAnimation();
        
        if (this.controls) {
            this.controls.dispose();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // 清理模型
        this.models.forEach(model => {
            if (model.geometry) model.geometry.dispose();
            if (model.material) {
                if (Array.isArray(model.material)) {
                    model.material.forEach(material => material.dispose());
                } else {
                    model.material.dispose();
                }
            }
        });
        
        this.models.clear();
        this.lights.clear();
        
        window.removeEventListener('resize', this.onWindowResize.bind(this));
        
        console.log('ThreeService 资源已清理');
    }
}

export default ThreeService; 