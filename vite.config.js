import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
// import VueDevTools from 'vite-plugin-vue-devtools' // 如果需要开发工具
// import eslint from 'vite-plugin-eslint' // 如果需要 ESLint

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    // 部署路径配置 - 根据实际部署情况调整
    base: mode === 'production' ? './' : '/', // 相对路径部署用 './'，根路径部署用 '/'

    plugins: [
      vue(),
      // VueDevTools(), // 开发工具插件
      // eslint(), // ESLint 插件
    ],

    // 路径别名配置
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@assets': resolve(__dirname, 'src/assets'),
        '@router': resolve(__dirname, 'src/router'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@api': resolve(__dirname, 'src/api'),
        '@store': resolve(__dirname, 'src/store')
      }
    },

    // 开发服务器配置
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 5173, // 指定端口
      open: true, // 自动打开浏览器
      cors: true, // 允许跨域
      // 代理配置
      proxy: {
        '/api': {
          target: 'http://*************:8099',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '/api')
        }
      }
    },

    // 构建配置
    build: {
      outDir: 'dist', // 构建输出目录
      assetsDir: 'assets', // 静态资源目录
      sourcemap: false, // 是否生成 source map
      minify: 'terser', // 压缩器
      chunkSizeWarningLimit: 2000, // chunk 大小警告限制
      rollupOptions: {
        output: {
          // 分包策略
          manualChunks: {
            vue: ['vue', 'vue-router'],
            elementPlus: ['element-plus', '@element-plus/icons-vue'],
            vendor: ['axios'] // 如果使用了 axios
          },
          // 文件命名
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    // 环境变量配置
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
    },

    // 预构建配置
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'element-plus',
        '@element-plus/icons-vue'
      ]
    }
  }
})
