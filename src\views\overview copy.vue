<template>
  <div class="left-content">
    <div class="container">
      <div class="container-item">
        <div class="content-header">机房信息</div>
        <div class="content-body jifang-info">
          <div class="jifang-info-item" @click="openModal('cabinet')">
            <img src="@assets/images/jifang-info-item1.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">16</span>
              <span class="jifang-info-item-value-unit">台</span>
            </div>
            <div class="jifang-info-item-title">
              <span>机柜总数</span>
            </div>
          </div>
          <div class="jifang-info-item" @click="openModal('device')">
            <img src="@assets/images/jifang-info-item2.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">136</span>
              <span class="jifang-info-item-value-unit">台</span>
            </div>
            <div class="jifang-info-item-title">
              <span>设备总数</span>
            </div>
          </div>
          <div class="jifang-info-item" @click="openModal('area')">
            <img src="@assets/images/jifang-info-item3.png" alt="" srcset="">
            <div class="jifang-info-item-value">
              <span class="jifang-info-item-value-num">117.67</span>
              <span class="jifang-info-item-value-unit">㎡</span>
            </div>
            <div class="jifang-info-item-title">
              <span>机房面积</span>
            </div>
          </div>
        </div>
      </div>
      <div class="container-item">
        <div class="content-header">设备容量</div>
        <div class="content-body">
          <DeviceCapacityChart 
            :storageUsed="storageUsed" 
            :storageTotal="storageTotal"
            :cpuUsed="cpuUsed"
            :cpuTotal="cpuTotal"
           />
        </div>
      </div>
      <div class="container-item">
        <div class="content-header">设备状态</div>
        <div class="content-body device-status-content">
          <!-- 左侧告警态势图表 -->
          <div class="device-status-left">
            <div class="status-stats">
              <div class="status-row">
                <div class="status-item blue">
                  <span class="status-dot"></span>
                  <span class="status-label">正常设备</span>
                  <span class="status-count">{{ deviceStatus.normal }}</span>
                </div>
                <div class="status-item green">
                  <span class="status-dot"></span>
                  <span class="status-label">主要设备</span>
                  <span class="status-count">{{ deviceStatus.main }}</span>
                </div>
              </div>
              <div class="status-row">
                <div class="status-item orange">
                  <span class="status-dot"></span>
                  <span class="status-label">警告设备</span>
                  <span class="status-count">{{ deviceStatus.warning }}</span>
                </div>
                <div class="status-item red">
                  <span class="status-dot"></span>
                  <span class="status-label">紧急设备</span>
                  <span class="status-count">{{ deviceStatus.urgent }}</span>
                </div>
              </div>
            </div>
            <div class="chart-container">
              <Pie3DChart :data="pieChartData" :height="'130px'" />
            </div>
          </div>
          
          <!-- 右侧设备状态信息 -->
          <div class="device-status-right">
            <div class="device-status-card normal-bg">
              <div class="device-status-label">正常设备</div>
              <div class="device-status-count">{{ deviceStatus.normal }}</div>
            </div>
            <div class="device-status-card main-bg">
              <div class="device-status-label">主要设备</div>
              <div class="device-status-count">{{ deviceStatus.main }}</div>
            </div>
            <div class="device-status-card warning-bg">
              <div class="device-status-label">警告设备</div>
              <div class="device-status-count">{{ deviceStatus.warning }}</div>
            </div>
            <div class="device-status-card urgent-bg">
              <div class="device-status-label">紧急设备</div>
              <div class="device-status-count">{{ deviceStatus.urgent }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="right-content">
    <div class="container right-container">
      <div class="container-item">
        <div class="content-header">
          设备最高温度监测
        </div>
        <div class="content-body temp-info">
          <div class="temp-top">
            <div class="temp-left-column">
              <div class="temp-top-item">
                <img class="temp-alarm-img" src="@assets/images/temp-alarm.png" alt="" srcset="">
                <div class="temp-alarm-info">
                  <div class="temp-alarm-info-title">今日最高温度</div>
                  <div class="temp-alarm-info-value">
                    <span class="temp-alarm-info-value-num">{{ maxTemperature }}</span>
                    <span class="temp-alarm-info-value-unit">℃</span>
                  </div>
                </div>
              </div>
              <div class="temp-top-item">
                <img class="temp-alarm-img" src="@assets/images/temp-alarm.png" alt="" srcset="">
                <div class="temp-alarm-info">
                  <div class="temp-alarm-info-title">今日最低温度</div>
                  <div class="temp-alarm-info-value">
                    <span class="temp-alarm-info-value-num">{{ minTemperature }}</span>
                    <span class="temp-alarm-info-value-unit">℃</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="temp-right-space">
              <TemperatureGauge 
                :temperature="envMonitoringData.avgTemp" 
                :min-temp="18" 
                :max-temp="35" 
              />
            </div>
          </div>
          <div class="temp-line">
            <LineChart :data="temperatureData" />
          </div>
        </div>
      </div>
      <div class="container-item">
        <div class="content-header">
          动环监测
          <!-- <div class="arrow-right-box">
            <CustomSelect 
              v-model="selectedRoom" 
              :options="roomOptions" 
              @change="handleRoomChange"
            />
          </div> -->
        </div>
        <div class="content-body donghuan-content">
          <!-- 温度范围和设备异常 -->
          <div class="donghuan-top">
            <div class="donghuan-range">
              <span class="range-dot blue-dot"></span>
              <span class="range-text-title">温度范围：</span><span class="range-text-value blue">{{ envMonitoringData.minTemp }} - {{ envMonitoringData.maxTemp }}</span><span
                class="range-text-unit">℃</span>
            </div>
            <!-- <div class="donghuan-range">
              <span class="range-dot red-dot"></span>
              <span class="range-text-title">设备异常数：</span><span class="range-text-value red">{{ envMonitoringData.abnormalDeviceCount }}</span><span
                class="range-text-unit">台</span>
            </div> -->
          </div>

          <!-- 环境监测卡片 -->
          <div class="env-cards">
            <div class="env-card">
              <img src="@assets/images/wendu.png" alt="" class="env-icon">
              <div class="env-info">
                <div class="env-title">温度</div>
                <div class="env-value">{{ envMonitoringData.avgTemp }}℃</div>
              </div>
            </div>
            <div class="env-card">
              <img src="@assets/images/shidu.png" alt="" class="env-icon">
              <div class="env-info">
                <div class="env-title">湿度</div>
                <div class="env-value orange">{{ envMonitoringData.avgHumidity }}%</div>
              </div>
            </div>
            <div class="env-card">
              <img src="@assets/images/pm2.5.png" alt="" class="env-icon">
              <div class="env-info">
                <div class="env-title">PM2.5</div>
                <div class="env-value">{{ envMonitoringData.avgPm25 }}</div>
              </div>
            </div>
          </div>

          <!-- 设备情况 -->
          <div class="device-section">
            <div class="device-title">设备情况</div>
            <div class="device-grid">
              <div class="device-item" :class="{ selected: selectedDevices.has('摄像头') }"
                @click="handleDeviceSelect('摄像头')">
                <img src="@assets/images/menjin.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">摄像头</div>
                  <div class="device-status">4</div>
                </div>
              </div>
              <div class="device-item" :class="{ selected: selectedDevices.has('门禁') }"
                @click="handleDeviceSelect('门禁')">
                <img src="@assets/images/menjin.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">门禁</div>
                  <div class="device-status">(2)</div>
                </div>
              </div>
              <!-- <div class="device-item" :class="{ selected: selectedDevices.has('精密列头柜') }"
                @click="handleDeviceSelect('精密列头柜')">
                <img src="@assets/images/jingmilietougui.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">精密列头柜</div>
                  <div class="device-status">1</div>
                </div>
              </div> -->
              <div class="device-item" :class="{ selected: selectedDevices.has('列间空调') }"
                @click="handleDeviceSelect('列间空调')">
                <img src="@assets/images/liejiankongtiao.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">列间空调</div>
                  <!-- <div class="device-status">(<span class="orange">1</span>/3)</div> -->
                  <div class="device-status">3</div>
                </div>
              </div>
              <div class="device-item" :class="{ selected: selectedDevices.has('机房空调') }"
                @click="handleDeviceSelect('机房空调')">
                <img src="@assets/images/jifangkongtiao.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">机房空调</div>
                  <div class="device-status">2</div>
                </div>
              </div>
              <div class="device-item" :class="{ selected: selectedDevices.has('配电柜') }"
                @click="handleDeviceSelect('配电柜')">
                <img src="@assets/images/dianbiao.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">配电柜</div>
                  <!-- <div class="device-status">(<span class="orange">1</span>/4)</div> -->
                  <div class="device-status">1</div>
                </div>
              </div>
              <!-- <div class="device-item" :class="{ selected: selectedDevices.has('温湿度设备') }"
                @click="handleDeviceSelect('温湿度设备')">
                <img src="@assets/images/wendu.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">温湿度设备</div>
                  <div class="device-status">1</div>
                </div>
              </div>
              <div class="device-item" :class="{ selected: selectedDevices.has('漏水') }"
                @click="handleDeviceSelect('漏水')">
                <img src="@assets/images/loushui.png" alt="" class="device-icon">
                <div class="device-info">
                  <div class="device-name">漏水</div>
                  <div class="device-status">1</div>
                </div>
              </div>
              <div class="device-item" :class="{ selected: selectedDevices.has('烟感') }"
                @click="handleDeviceSelect('烟感')">
                <img src="@assets/images/yangan.png" alt="" class="device-icon">
                <div class="device-info"> 
                  <div class="device-name">烟感</div>
                  <div class="device-status">1</div>
                </div>
              </div> -->
            </div>
          </div>

          <!-- UPS信息 -->
          <div class="ups-section">
            <div class="ups-header">
              <!-- <div class="ups-title">UPS信息</div> -->
              <CustomSelect 
                v-model="selectedUps" 
                :options="upsOptions" 
                @change="selectUps"
              />
              <!-- <div class="ups-selector">
                <div class="selector-item" :class="{ active: selectedUps === 'ups1' }" @click="selectUps('ups1')">UPS1</div>
                <div class="selector-item" :class="{ active: selectedUps === 'ups2' }" @click="selectUps('ups2')">UPS2</div>
              </div> -->
            </div>
            <div class="ups-content">
              <div class="ups-data-row">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1367.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-label">输入电压</div>
                  <div class="ups-value">{{ currentUpsData.inputVoltage }}V</div>
                </div>
              </div>
              <div class="ups-data-row elevated">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1368.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-label">输入电流</div>
                  <div class="ups-value">{{ currentUpsData.inputCurrent }}A</div>
                </div>
              </div>
              <div class="ups-data-row elevated">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1369.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-label">输出电压</div>
                  <div class="ups-value">{{ currentUpsData.outputVoltage }}V</div>
                </div>
              </div>
              <div class="ups-data-row">
                <div class="ups-img-wrapper">
                  <img src="@assets/images/Group 1370.png" alt="" class="ups-bg-img">
                </div>
                <div class="ups-text">
                  <div class="ups-label">输出电流</div>
                  <div class="ups-value">{{ currentUpsData.outputCurrent }}A</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="bottom-alarms">
    <div class="alarm-item" v-for="alarm in alarmStats" :key="alarm.label">
      <img :src="alarm.img" alt="" class="alarm-bg">
      <div class="alarm-content">
        <div class="alarm-label">{{ alarm.label }}</div>
        <div class="alarm-value" :style="{ color: alarm.valueColor }">{{ alarm.value }}</div>
      </div>
    </div>
  </div>

  <!-- Modal -->
  <div v-if="isModalVisible" class="modal-content">
    <div class="modal-header">
      <span class="modal-title">{{ modalTitle }}</span>
      <img src="@/assets/images/close.png" alt="" srcset="" class="close-btn" @click="closeModal">
    </div>
    <div class="modal-body">
      <img v-if="modalType === 'image'" :src="modalImageSrc" alt="Modal Image" class="modal-image">
      <div v-if="modalType === 'table'" class="modal-table">
        <div class="table-header">
          <div class="th">设备名称</div>
          <div class="th">设备类型</div>
          <div class="th">在线状态</div>
          <div class="th">告警状态</div>
          <div class="th">所属机柜</div>
        </div>
        <div class="table-body">
          <div v-if="!isLoadingDeviceList && deviceList.length > 0">
            <div class="tr" v-for="item in deviceList" :key="item.deviceId">
              <div class="td">{{ item.deviceName }}</div>
              <div class="td">{{ item.deviceType === '1' ? '网络设备' : '主机' }}</div>
              <div class="td">{{ item.device_online == 0 ? '在线' : '离线' }}</div>
              <div class="td">{{ item.alarmStatus === '0' ? '正常' : '告警' }}</div>
              <div class="td">{{ item.cabinetId ? `机柜${item.cabinetId}` : 'N/A' }}</div>
            </div>
          </div>
          <div v-else class="loading-text">
            {{ isLoadingDeviceList ? '加载中...' : '暂无数据' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import DeviceCapacityChart from '@components/DeviceCapacityChart.vue'
import LineChart from '@components/LineChart.vue'
import CustomSelect from '@components/CustomSelect.vue'
import TemperatureGauge from '@components/TemperatureGauge.vue'
import Pie3DChart from '@components/Pie3DChart.vue'
import { get } from '@/api/request.js'
import statImg1 from '@assets/images/dashborad/Group 1335.png'
import statImg2 from '@assets/images/dashborad/Group 1335 (1).png'
import statImg3 from '@assets/images/dashborad/Group 1335 (2).png'
import statImg4 from '@assets/images/dashborad/Group 1335 (3).png'
import cabinetImage from '@assets/images/dashborad/机柜.png'
import areaImage from '@assets/images/dashborad/平面图.png'

const router = useRouter()

const alarmStats = ref([
  {
    img: statImg1,
    label: '紧急告警',
    value: '0',
    valueColor: '#FF7667'
  },
  {
    img: statImg2,
    label: '重要告警',
    value: '0',
    valueColor: '#FFB800'
  },
  {
    img: statImg3,
    label: '次要告警',
    value: '0',
    valueColor: '#00D1FF'
  },
  {
    img: statImg4,
    label: '提示告警',
    value: '0',
    valueColor: '#FFFFFF'
  }
])

// Modal state
const isModalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('') // 'image' or 'table'
const modalImageSrc = ref('')
const deviceList = ref([])
const isLoadingDeviceList = ref(false)

const openModal = async (type) => {
  if (type === 'cabinet') {
    modalTitle.value = '机柜总数'
    modalType.value = 'image'
    modalImageSrc.value = cabinetImage
  } else if (type === 'area') {
    modalTitle.value = '机房面积'
    modalType.value = 'image'
    modalImageSrc.value = areaImage
  } else if (type === 'device') {
    modalTitle.value = '设备总数'
    modalType.value = 'table'
    await fetchJgquipmentList()
  }
  isModalVisible.value = true
}

const closeModal = () => {
  isModalVisible.value = false
  // Reset states
  modalTitle.value = ''
  modalType.value = ''
  modalImageSrc.value = ''
  deviceList.value = []
}

const fetchJgquipmentList = async () => {
  isLoadingDeviceList.value = true
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/jgquipmentList')
    const result = await response.json()
    if (result.code === 0 && Array.isArray(result.data)) {
      deviceList.value = result.data
    } else {
      console.error('获取设备列表失败:', result.msg)
      deviceList.value = []
    }
  } catch (error) {
    console.error('请求设备列表失败:', error)
    deviceList.value = []
  } finally {
    isLoadingDeviceList.value = false
  }
}

// 容量数据
const cpuUsed = ref(0)
const cpuTotal = ref(0)
const storageUsed = ref(0)
const storageTotal = ref(0)

// 设备状态数据
const deviceStatus = ref({
  normal: 118,
  main: 12,
  warning: 6,
  urgent: 0
})

// 饼图数据
const pieChartData = computed(() => [
  { name: '正常设备', value: deviceStatus.value.normal, color: '#218CDF' },
  { name: '主要设备', value: deviceStatus.value.main, color: '#09D8DB' },
  { name: '警告设备', value: deviceStatus.value.warning, color: '#FCB24A' },
  { name: '紧急设备', value: deviceStatus.value.urgent, color: '#FF744C' }
])

// 温度监测数据
const temperatureData = ref([
  { time: '3:00', temperature: 15 },
  { time: '6:00', temperature: 25 },
  { time: '9:00', temperature: 24 },
  { time: '12:00', temperature: 12 },
  { time: '15:00', temperature: 28 },
  { time: '18:00', temperature: 30 },
  { time: '21:00', temperature: 24 }
])

// 温度监测接口数据
const maxTemperature = ref('--') // 最高温度
const minTemperature = ref('--') // 最低温度
const abnormalDeviceCount = ref(0) // 异常设备数
const temperatureLineData = ref([]) // 温度趋势线数据
const isLoadingTempData = ref(false) // 加载状态

// 动环监测数据
const envMonitoringData = ref({
  abnormalDeviceCount: 0,
  minTemp: '--',
  avgHumidity: '--',
  avgTemp: '--',
  maxTemp: '--',
  avgPm25: '--'
})

// 机房选择相关
const selectedRoom = ref('A机房')
const roomOptions = ref([
  { value: 'A机房', label: 'A机房' },
  { value: 'B机房', label: 'B机房' },
  { value: 'C机房', label: 'C机房' },
  { value: 'D机房', label: 'D机房' }
])

// 设备选择状态
const selectedDevices = ref(new Set(['摄像头', '列间空调', '机房空调', '配电柜']))

// 设备类型映射
const deviceTypeMap = {
  '摄像头': '摄像头',
  '门禁': '门禁',
  '精密列头柜': '精密列头柜',
  '列间空调': '列间空调',
  '机房空调': '机房空调',
  '配电柜': '配电柜',
  '温湿度设备': '温湿度设备',
  '漏水': '漏水',
  '烟感': '烟感'
}

// UPS相关数据
const selectedUps = ref('ups1') // 当前选中的UPS
const upsOptions = ref([
  { value: 'ups1', label: 'UPS1' },
  { value: 'ups2', label: 'UPS2' }
])
const upsDevicesData = ref({
  ups1: {
    inputVoltage: '--',
    inputCurrent: '--',
    outputVoltage: '--',
    outputCurrent: '--'
  },
  ups2: {
    inputVoltage: '--',
    inputCurrent: '--',
    outputVoltage: '--',
    outputCurrent: '--'
  }
})

// 当前显示的UPS数据
const currentUpsData = ref(upsDevicesData.value.ups1)

// 处理设备选择
const handleDeviceSelect = (deviceType) => {
  console.log('用户点击设备:', deviceType)

  if (selectedDevices.value.has(deviceType)) {
    selectedDevices.value.delete(deviceType)
    console.log('取消选择设备:', deviceType)
  } else {
    selectedDevices.value.add(deviceType)
    console.log('选择设备:', deviceType)
  }

  console.log('当前选中的设备:', Array.from(selectedDevices.value))

  // 触发设备显示/隐藏逻辑
  updateDeviceVisibility()
}

// 更新设备可见性
const updateDeviceVisibility = () => {
  // 通过事件通知ThreeScene组件更新设备显示
  window.dispatchEvent(new CustomEvent('deviceSelectionChanged', {
    detail: {
      selectedDevices: Array.from(selectedDevices.value),
      showAll: selectedDevices.value.size === 0
    }
  }))
}

// 处理机房切换
const handleRoomChange = (value) => {
  console.log('切换到机房:', value)
  // 这里可以添加切换机房的逻辑，比如更新动环监测数据等
}

// 选择UPS设备
const selectUps = (upsType) => {
  selectedUps.value = upsType
  currentUpsData.value = upsDevicesData.value[upsType]
  console.log('切换到UPS:', upsType, currentUpsData.value)
}

// 获取设备列表数据
const fetchDeviceList = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/devicelist?remark=A')
    const result = await response.json()

    if (result.code === 0 && Array.isArray(result.data)) {
      const devices = result.data

      // 处理UPS设备数据
      const ups1Device = devices.find(device => device.deviceName === '1号UEUPS')
      const ups2Device = devices.find(device => device.deviceName === '2号UEUPS')

      if (ups1Device && ups1Device.canshu) {
        const ups1Data = extractUpsData(ups1Device.canshu)
        upsDevicesData.value.ups1 = ups1Data
        if (selectedUps.value === 'ups1') {
          currentUpsData.value = ups1Data
        }
      }

      if (ups2Device && ups2Device.canshu) {
        const ups2Data = extractUpsData(ups2Device.canshu)
        upsDevicesData.value.ups2 = ups2Data
        if (selectedUps.value === 'ups2') {
          currentUpsData.value = ups2Data
        }
      }

      // 处理动环监测数据
      processEnvironmentData(devices)

      console.log('设备数据加载成功:', {
        ups1: upsDevicesData.value.ups1,
        ups2: upsDevicesData.value.ups2,
        envData: envMonitoringData.value
      })
    } else {
      console.error('获取设备列表失败:', result.msg)
    }
  } catch (directError) {
    console.error('直接请求失败:', directError)
  }
}

// 从参数数组中提取UPS数据
const extractUpsData = (canshu) => {
  const data = {
    inputVoltage: '--',
    inputCurrent: '--',
    outputVoltage: '--',
    outputCurrent: '--'
  }

  if (!Array.isArray(canshu)) {
    return data
  }

  canshu.forEach(param => {
    if (param.signalName === '输入A相电压') {
      data.inputVoltage = param.currentValue || '--'
    } else if (param.signalName === '输入A相电流') {
      data.inputCurrent = param.currentValue || '--'
    } else if (param.signalName === '输出A相电压') {
      data.outputVoltage = param.currentValue || '--'
    } else if (param.signalName === '输出A相电流') {
      data.outputCurrent = param.currentValue || '--'
    }
  })

  return data
}

// 处理动环监测数据
const processEnvironmentData = (devices) => {
  const temperatures = []
  const humidities = []
  let pm25Value = '--'

  devices.forEach(device => {
    if (!device.canshu || !Array.isArray(device.canshu)) return

    device.canshu.forEach(param => {
      const value = parseFloat(param.currentValue)
      if (isNaN(value)) return

      // 收集温度数据
      if (param.signalName.includes('温度') && (param.unit === '°C' || param.unit === '℃')) {
        temperatures.push(value)
      }
      
      // 收集湿度数据
      if (param.signalName.includes('湿度') && (param.unit === '%' || param.unit === 'RH%')) {
        humidities.push(value)
      }

      // 如果有PM2.5数据（虽然当前数据中没有，但保留逻辑）
      if (param.signalName.includes('PM2.5') || param.signalName.includes('pm2.5')) {
        pm25Value = value
      }
    })
  })

  // 计算统计值
  const parseValue = (val) => {
    return isNaN(val) ? '--' : Number(val.toFixed(1))
  }

  if (temperatures.length > 0) {
    const minTemp = Math.min(...temperatures)
    const maxTemp = Math.max(...temperatures)
    const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length

    envMonitoringData.value.minTemp = parseValue(minTemp)
    envMonitoringData.value.maxTemp = parseValue(maxTemp)
    envMonitoringData.value.avgTemp = parseValue(avgTemp)

    // 同步更新最高最低温度显示
    maxTemperature.value = parseValue(maxTemp)
    minTemperature.value = parseValue(minTemp)
  }

  if (humidities.length > 0) {
    const avgHumidity = humidities.reduce((sum, humidity) => sum + humidity, 0) / humidities.length
    envMonitoringData.value.avgHumidity = parseValue(avgHumidity)
  }

  // PM2.5暂时使用默认值（数据中没有）
  envMonitoringData.value.avgPm25 = pm25Value === '--' ? '12' : parseValue(pm25Value)

  // 异常设备数量暂时使用默认值
  envMonitoringData.value.abnormalDeviceCount = 0

  console.log('动环监测数据处理完成:', {
    temperatures,
    humidities,
    result: envMonitoringData.value
  })
}

// 获取动环监测数据
const fetchEnvMonitoringData = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/rotatingRingMonitor')
    const result = await response.json()
    if (result.code === 0 && result.data) {
      const data = result.data
      const parseValue = (str) => {
        if (typeof str !== 'string') return str
        const val = parseFloat(str)
        return isNaN(val) ? '--' : val
      }
      envMonitoringData.value = {
        abnormalDeviceCount: parseValue(data.ycsb),
        minTemp: parseValue(data.zxwd),
        avgHumidity: parseValue(data.sdavg),
        avgTemp: parseValue(data.wdavg),
        maxTemp: parseValue(data.zgwd),
        avgPm25: parseValue(data.pmavg)
      }
      
      // 同步更新最高最低温度显示
      if (maxTemperature.value === '--' && envMonitoringData.value.maxTemp !== '--') {
        maxTemperature.value = envMonitoringData.value.maxTemp
      }
      if (minTemperature.value === '--' && envMonitoringData.value.minTemp !== '--') {
        minTemperature.value = envMonitoringData.value.minTemp
      }
      
      console.log('动环监测数据加载成功（直接请求）:', envMonitoringData.value)
    } else {
      console.error('获取动环监测数据失败:', result.msg)
    }
  } catch (directError) {
    console.error('直接请求也失败:', directError)
  }
}

// 获取设备容量数据
const fetchDeviceCapacity = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/equipmentCapacity')
    const result = await response.json()
    if (result.code === 0 && result.data) {
      const total = parseFloat(result.data.cpuMb)
      const used = parseFloat(result.data.cpuUsed)

      if (!isNaN(total) && !isNaN(used)) {
        cpuTotal.value = total
        cpuUsed.value = used
      }

      const sTotal = parseFloat(result.data.storageMb)
      const sUsed = parseFloat(result.data.storageUsed)
      if (!isNaN(sTotal) && !isNaN(sUsed)) {
        storageTotal.value = sTotal
        storageUsed.value = sUsed
      }

    } else {
      console.error('获取设备容量数据失败:', result.msg)
    }
  } catch (error) {
    console.error('请求设备容量数据失败:', error)
  }
}

// 获取告警统计数据
const fetchAlarmStats = async () => {
  try {
    const response = await fetch('http://*************:8099/api/zjRotatingRing/alarmByTypeStatistics')
    const result = await response.json()

    if (result.code === 0 && Array.isArray(result.data)) {
      const apiData = result.data
      const dataMap = new Map(apiData.map(item => [item.name, item.value]))

      const alarmMapping = {
        '紧急告警': '1',
        '重要告警': '2',
        '次要告警': '3',
        '提示告警': '4'
      }

      alarmStats.value.forEach(stat => {
        const apiKey = alarmMapping[stat.label]
        if (dataMap.has(apiKey)) {
          stat.value = dataMap.get(apiKey).toString()
        } else {
          stat.value = '0'
        }
      })
      console.log('告警统计数据加载成功:', alarmStats.value)
    } else {
      console.error('获取告警统计数据失败:', result.msg)
    }
  } catch (error) {
    console.error('请求告警统计数据失败:', error)
  }
}

// 获取温度趋势统计数据
const fetchMaxTempStatistics = async () => {
  try {
    isLoadingTempData.value = true
    const response = await fetch('http://*************:8099/api/zjRotatingRing/maxTempStatistics')
    const result = await response.json()

    if (result.code === 0) {
      const data = result.data
      
      // 只在设备列表数据还没有加载时才使用这里的温度数据作为fallback
      if (maxTemperature.value === '--' && data.zgwd) {
        maxTemperature.value = data.zgwd
      }
      if (minTemperature.value === '--' && data.zxwd) {
        minTemperature.value = data.zxwd
      }
      
      abnormalDeviceCount.value = data.ycsb || 0

      // 处理温度趋势线数据
      if (data.wdLine && Array.isArray(data.wdLine)) {
        temperatureLineData.value = data.wdLine.map(item => ({
          time: `${item.hour}:00`,
          temperature: parseFloat(item.zgwd) || 0
        }))
        temperatureData.value = temperatureLineData.value
      }

      console.log('温度趋势数据加载成功:', {
        maxTemp: maxTemperature.value,
        minTemp: minTemperature.value,
        abnormalCount: abnormalDeviceCount.value,
        lineData: temperatureLineData.value
      })
    } else {
      console.error('获取温度统计数据失败:', result.msg)
    }
  } catch (directError) {
    console.error('获取温度趋势数据失败:', directError)
  } finally {
    isLoadingTempData.value = false
  }
}

// 监听3D模型加载完成事件
const handleModelLoaded = () => {
  console.log('3D模型加载完成，应用默认设备选择')
  console.log('当前选中的设备:', Array.from(selectedDevices.value))
  // 延迟一小段时间确保3D场景完全初始化
  setTimeout(() => {
    updateDeviceVisibility()
    console.log('已发送设备选择变化事件')
  }, 500)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAlarmStats()
  fetchMaxTempStatistics()
  fetchDeviceList() // 这个函数现在包含了动环监测数据处理
  fetchDeviceCapacity()

  // 监听3D模型加载完成事件
  window.addEventListener('threeSceneReady', handleModelLoaded)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('threeSceneReady', handleModelLoaded)
})
</script>

<style scoped lang="scss">
.left-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 480px;
  height: 100%;
  background: url('@assets/images/left-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;

  .container {
    margin-left: 17px;
  }
}

.right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 529px;
  height: 100%;
  background: url('@assets/images/right-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;

  .container {
    margin-right: 17px;
    margin-left: auto;
  }
}

.container {
  width: 456px;
  height: 968px;
  background: linear-gradient(271deg, rgba(6, 25, 55, 0.3) 0%, rgba(9, 61, 102, 0.4) 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  // 毛玻璃效果
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.container-item {}

.content-header {
  width: 100%;
  height: 54px;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  line-height: 54px;
  padding-left: 34px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .arrow-right-box {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .arrow-right {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}

.jifang-info {
  width: 100%;
  padding: 0 18px 34px 18px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;

  .jifang-info-item {
    width: 123px;
    height: 235px;
    background: url('@assets/images/jifang-info-bg.png') no-repeat center center / 100% 100%;
    padding-top: 35px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.2);
    }

    .jifang-info-item-value {
      margin-top: 21px;

      .jifang-info-item-value-num {
        font-family: DIN Alternate;
        font-weight: bold;
        font-size: 22px;
        color: #FFFFFF;
        letter-spacing: 1px;
      }

      .jifang-info-item-value-unit {
        font-weight: 400;
        font-size: 12px;
        color: #9FB0D8;
      }
    }

    .jifang-info-item-title {
      font-weight: 500;
      font-size: 14px;
      margin-top: 22px;
    }
  }
}

.idc-img {
  width: 422px;
  height: 291px;
  margin-left: 17px;
  margin-top: 9px;
}

.temp-info {
  width: 100%;
  padding: 15px 29px 0 29px;
  box-sizing: border-box;

  .temp-top {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .temp-left-column {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .temp-right-space {
      width: 180px;
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .temp-top-item {
      width: 182px;
      height: 68px;
      padding: 10px 0 16px 8px;
      box-sizing: border-box;
      background: url('@assets/images/temp-bg.png') no-repeat center center / 100% 100%;
      display: flex;
      align-items: center;

      .temp-alarm-img {
        width: 42px;
        height: 42px;
        margin-right: 12px;
      }

      .temp-alarm-info {
        .temp-alarm-info-title {
          font-weight: 400;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .temp-alarm-info-value {

          .temp-alarm-info-value-num {
            font-family: DIN Alternate, DIN Alternate;
            font-weight: bold;
            font-size: 16px;
            color: #B0E0FF;
          }

          .temp-alarm-info-value-unit {
            font-weight: 400;
            font-size: 12px;
            color: #B0E0FF;
            margin-left: 5px;
          }
        }
      }

    }
  }

  .temp-line {
    margin-top: 20px;
    padding: 0 10px;
  }
}

// 动环监测样式
.donghuan-content {
  width: 100%;
  padding: 4px 13px;
  box-sizing: border-box;
}

.donghuan-top {
  display: flex;
  justify-content: space-around;
  margin-bottom: 6px;
  padding: 12px 16px;
  background: url('@assets/images/line_img_1.png') no-repeat center center / 100% 100%;
  box-sizing: border-box;

}

.donghuan-range {
  display: flex;
  align-items: center;

  .range-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;

    &.blue-dot {
      background: #40AAFC;
    }

    &.red-dot {
      background: #FD8D79;
    }
  }

  .range-text-title {
    font-weight: 500;
    font-size: 14px;
  }

  .range-text-value {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 20px;
  }

  .blue {
    color: #40AAFC;
  }

  .red {
    color: #FD8D79;
  }

  .range-text-unit {
    font-weight: 400;
    font-size: 14px;
    color: #97B0C8;
    margin-left: 7px;
  }
}

.env-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.env-card {
  width: 148px;
  height: 48px;
  padding: 4px 0px 4px 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: url('@assets/images/env-card-bg.png') no-repeat center center / 100% 100%;

  .env-icon {
    width: 18px;
    height: 18px;
    margin-right: 23px;
  }

  .env-info {
    .env-title {
      font-weight: 400;
      font-size: 12px;
    }

    .env-value {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 20px;
      color: #EBF1FF;
      letter-spacing: 1px;

      &.orange {
        color: #FD8D79;
      }
    }
  }
}

.device-section {
  margin-bottom: 2px;
}

.device-title {
  position: relative;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 7px;
  margin-left: 14px;

  &::before {
    position: absolute;
    top: 8px;
    left: -14px;
    content: '';
    display: block;
    width: 6px;
    height: 4px;
    background: #27ABFF;
  }
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0px;
}

.device-item {
  width: 148px;
  height: 48px;
  padding: 4px 0px 4px 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: url('@assets/images/env-card-bg.png') no-repeat center center / 100% 100%;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    filter: brightness(1.1);
  }

  &.selected {
    background-color: rgba(39, 171, 255, 0.2);
    border: 1px solid #27ABFF;
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.4);
  }

  .device-icon {
    width: 18px;
    height: 18px;
    margin-right: 23px;
  }

  .device-info {
    .device-name {
      font-weight: 400;
      font-size: 14px;
    }

    .device-status {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 20px;
      color: #EBF1FF;
      letter-spacing: 1px;

      .orange {
        color: #FD8D79;
      }
    }
  }
}

.ups-section {
  width: 100%;
  height: 212px;

  .ups-header {
    width: 100%;
    height: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 20px;
    box-sizing: border-box;

    .ups-title {
      font-weight: 400;
      font-size: 14px;
      color: #ADF1FF;
    }

    .ups-selector {
      display: flex;
      gap: 10px;

      .selector-item {
        padding: 4px 12px;
        font-size: 14px;
        color: rgba(#FFFFFF, 0.5);
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          color: #FFFFFF;
          background: rgba(#27ABFF, 0.2);
        }

        &.active {
          color: #FFFFFF;
          background: rgba(#27ABFF, 0.3);
          box-shadow: 0 0 10px rgba(#27ABFF, 0.2);
        }
      }
    }
  }

  .ups-content {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;
    height: 140px;
  }

  .ups-data-row {
    position: relative;
    width: 90px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;

    &.elevated {
      transform: translateY(-15px);
    }

    .ups-img-wrapper {
      width: 90px;
      height: 90px;
      position: relative;

      .ups-bg-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }

    .ups-text {
      position: absolute;
      bottom: 10px;
      left: 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
      padding: 5px 0;
    }

    .ups-label {
      font-size: 12px;
      color: #B2F3FF;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .ups-value {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }
}

.bottom-alarms {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  pointer-events: all;
}

.alarm-item {
  position: relative;
  width: 240px; /* 根据实际图片大小调整 */
  height: 90px; /* 根据实际图片大小调整 */
}

.alarm-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.alarm-content {
  position: absolute;
  top: 50%;
  left: 33%;
  transform: translateY(-50%);
  text-align: left;
  color: #fff;
}

.alarm-label {
  font-size: 14px;
  color: #a3c0e3;
}

.alarm-value {
  font-size: 24px;
  font-weight: bold;
  font-family: DIN Alternate, DIN Alternate;
}

// 设备状态样式
.device-status-content {
  width: 100%;
  padding: 15px 18px;
  box-sizing: border-box;
  display: flex;
  gap: 15px;
}

.device-status-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-status-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 设备状态统计样式
.status-stats {
  margin-bottom: 12px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #C0DAEA;

  .status-dot {
    width: 6px;
    height: 6px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .status-label {
    margin-right: 8px;
    font-weight: 400;
    font-size: 12px;
    color: #A9C5C8;
  }

  .status-count {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 14px;
    color: #DAECF6;
  }

  &.green .status-dot {
    background-color: #09D8DB;
  }

  &.blue .status-dot {
    background-color: #218CDF;
  }

  &.orange .status-dot {
    background-color: #FCB24A;
  }

  &.red .status-dot {
    background-color: #FF744C;
  }
}

.chart-container {
  height: 130px;
  flex: 1;
}

// 设备状态卡片样式
.device-status-card {
  width: 100%;
  height: 48px;
  padding: 8px 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;

  &.normal-bg {
    background-image: url('@assets/images/dashborad/正常设备.png');
  }

  &.main-bg {
    background-image: url('@assets/images/dashborad/未注册设备.png');
  }

  &.warning-bg {
    background-image: url('@assets/images/dashborad/告警设备.png');
  }

  &.urgent-bg {
    background-image: url('@assets/images/dashborad/离线设备.png');
  }

  .device-status-label {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .device-status-count {
    position: absolute;
    right: 15px;
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Modal styles - 模仿ThreeScene弹窗样式
.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 852px;
  height: 513px;
  background: #14354E;
  border-radius: 4px;
  z-index: 10001;
  overflow: hidden;
  color: #fff;
  pointer-events: auto; /* 确保弹窗自身可以接收点击事件 */
}

.modal-header {
  height: 54px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;

  .modal-title {
    margin-left: 55px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
  }

  .close-btn {
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin-right: 26px;
  }
}

.modal-body {
  overflow: hidden;
  height: calc(100% - 54px);
  padding: 0;
  box-sizing: border-box;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 20px;
  box-sizing: border-box;
}

.modal-table {
  height: 100%;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 200px 140px 120px 120px 1fr;
  padding: 20px 56px;
  box-sizing: border-box;
}

.th {
  font-weight: 500;
  font-size: 14px;
  color: #7BCCFF;
}

.table-body {
  height: calc(100% - 62px); /* 513px total height - 54px header - 62px table-header ~= 397px */
  overflow-y: auto;
  padding: 0 24px;
}

.tr {
  display: grid;
  grid-template-columns: 200px 140px 120px 120px 1fr;
  height: 51px;
  line-height: 51px;
  border-radius: 0px;
  border: 1px solid rgba(151, 151, 151, 0.1);
  padding: 0 30px;
  box-sizing: border-box;
  margin-bottom: 2px;
}

.tr:hover {
  background-color: rgba(39, 171, 255, 0.1);
}

.td {
  font-weight: 400;
  font-size: 16px;
  display: flex;
  align-items: center;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loading-text {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #A9C5C8;
}

/* 滚动条样式 */
.table-body::-webkit-scrollbar {
  width: 6px;
}

.table-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb {
  background: rgba(39, 171, 255, 0.5);
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: rgba(39, 171, 255, 0.8);
}
</style>