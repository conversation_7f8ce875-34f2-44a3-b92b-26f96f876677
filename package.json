{"name": "computerroomlargescreen", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "element-plus": "^2.4.4", "three": "^0.177.0", "vue": "^3.5.13", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass": "^1.69.5", "terser": "^5.16.1", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-devtools": "^7.0.0"}}