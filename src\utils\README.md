# ThreeService 使用文档

ThreeService 是一个封装了 Three.js 常用功能的服务类，用于快速构建3D场景。

## 特性

- 🎯 简化的 API 接口
- 🔧 可配置化初始化
- 📦 内置模型加载器
- 🎮 自带轨道控制器
- 💡 预配置光照系统
- 🧹 完整的资源管理

## 基本使用

### 初始化场景

```javascript
import ThreeService from '@/utils/ThreeService.js';

const threeService = new ThreeService();

// 初始化场景
const success = threeService.init('container-id', {
    camera: {
        position: { x: 0, y: 10, z: 20 }
    },
    controls: {
        maxDistance: 100,
        minDistance: 5
    }
});

if (success) {
    // 开始动画循环
    threeService.startAnimation();
}
```

### 加载GLB模型

```javascript
try {
    const result = await threeService.loadGLBModel('/path/to/model.glb', {
        name: 'myModel',
        scale: 1.5,
        position: { x: 0, y: 0, z: 0 },
        metalness: 0.3,
        roughness: 0.7,
        onProgress: (percent) => {
            console.log(`加载进度: ${percent}%`);
        }
    });
    
    console.log('模型加载成功:', result.name);
} catch (error) {
    console.error('模型加载失败:', error);
}
```

### 创建基础几何体

```javascript
const cube = threeService.createCube({
    name: 'myCube',
    width: 2,
    height: 2,
    depth: 2,
    color: 0x00f0ff,
    position: { x: 0, y: 1, z: 0 },
    transparent: true,
    opacity: 0.8
});
```

## 配置选项

### 初始化配置

```javascript
const config = {
    scene: {
        background: 0x1a1a1a,
        fog: {
            color: 0x404040,
            near: 10,
            far: 100
        }
    },
    camera: {
        fov: 75,
        near: 0.1,
        far: 1000,
        position: { x: 0, y: 10, z: 20 }
    },
    renderer: {
        antialias: true,
        alpha: true,
        shadowMap: true
    },
    controls: {
        enableDamping: true,
        dampingFactor: 0.05,
        maxDistance: 100,
        minDistance: 5
    }
};
```

### 模型加载选项

```javascript
const options = {
    name: 'modelName',           // 模型名称
    scale: 1,                    // 缩放比例
    position: { x: 0, y: 0, z: 0 }, // 位置
    rotation: { x: 0, y: 0, z: 0 }, // 旋转
    metalness: 0.3,              // 金属度
    roughness: 0.7,              // 粗糙度
    enableShadow: true,          // 启用阴影
    materialEnhance: true,       // 材质增强
    autoFitCamera: true,         // 自动调整相机
    onProgress: (percent) => {}  // 进度回调
};
```

## API 方法

### 场景控制

- `init(containerId, options)` - 初始化场景
- `startAnimation()` - 开始动画循环
- `stopAnimation()` - 停止动画循环
- `dispose()` - 销毁场景和资源

### 模型管理

- `loadGLBModel(url, options)` - 加载GLB模型
- `createCube(options)` - 创建立方体
- `getModel(name)` - 获取模型
- `fitCameraToModel(model)` - 调整相机适应模型

### 获取对象

- `scene` - Three.js场景对象
- `camera` - 相机对象
- `renderer` - 渲染器对象
- `controls` - 控制器对象
- `getLight(name)` - 获取光源

## 在Vue组件中使用

```vue
<template>
    <div id="three-container"></div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';
import ThreeService from '@/utils/ThreeService.js';

let threeService = null;

onMounted(async () => {
    threeService = new ThreeService();
    
    if (threeService.init('three-container')) {
        threeService.startAnimation();
        
        // 加载模型
        await threeService.loadGLBModel('/model/scene.glb', {
            name: 'scene',
            autoFitCamera: true
        });
    }
});

onUnmounted(() => {
    if (threeService) {
        threeService.dispose();
    }
});
</script>

<style scoped>
#three-container {
    width: 100%;
    height: 500px;
}
</style>
```

## 注意事项

1. **资源管理**: 使用完毕后记得调用 `dispose()` 方法清理资源
2. **容器元素**: 确保容器元素在初始化时已存在于DOM中
3. **模型路径**: GLB模型文件需要放在public目录下
4. **性能优化**: 对于复杂场景，可以通过配置控制渲染质量

## 扩展功能

ThreeService 支持扩展，你可以：

- 添加更多几何体创建方法
- 扩展材质系统
- 增加动画功能
- 集成物理引擎
- 添加后处理效果 