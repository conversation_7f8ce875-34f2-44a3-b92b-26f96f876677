<template>
    <div ref="chartContainer" class="temperature-chart"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref(null)
let chartInstance = null

const props = defineProps({
    data: {
        type: Array,
        default: () => [
            { time: '3:00', temperature: 15 },
            { time: '6:00', temperature: 25 },
            { time: '9:00', temperature: 24 },
            { time: '12:00', temperature: 12 },
            { time: '15:00', temperature: 28 },
            { time: '18:00', temperature: 30 },
            { time: '21:00', temperature: 24 },
            { time: '24:00', temperature: 18 }
        ]
    },
    type: {
        type: String,
        default: '1'
    },
    unit: {
        type: String,
        default: '℃'
    }
})

const initChart = () => {
    if (!chartContainer.value) return

    chartInstance = echarts.init(chartContainer.value)

    const times = props.data.map(item => item.time)
    const temperatures = props.data.map(item => item.temperature)
    
    // 根据type选择主题色
    const getThemeColors = () => {
        if (props.type === '2') {
            // 蓝色主题
            return {
                primary: 'rgba(39, 171, 255, 1)',
                secondary: 'rgba(39, 171, 255, 0.5)',
                light: 'rgba(39, 171, 255, 0.05)',
                shadow: 'rgba(39, 171, 255, 1)'
            }
        } else {
            // 默认橙色主题
            return {
                primary: 'rgba(239, 135, 110, 1)',
                secondary: 'rgba(239, 135, 110, 0.5)',
                light: 'rgba(239, 135, 110, 0.05)',
                shadow: 'rgba(239, 135, 110, 1)'
            }
        }
    }
    
    const colors = getThemeColors()

    const option = {
        grid: {
            left: '1%',
            right: '0%',
            top: '16%',
            bottom: '1%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: times,
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#ADC0D0',
                    width: 1
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#C0DAEA',
                fontSize: 14,
                fontFamily: 'pingfang'
            },
            splitLine: {
                show: false,
            }
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 30,
            interval: 6,
            name: `单位：${props.unit}`,
            nameLocation: 'end',
            nameTextStyle: {
                color: '#BDD9EF',
                fontSize: 14,
                fontFamily: 'pingfang',
                padding: [0, 0, 0, 0]
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#ADC0D0',
                    width: 1
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#C0DAEA',
                fontSize: 14,
                fontFamily: 'pingfang'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(151, 151, 151, 0.5)',
                    width: 1,
                    type: 'dashed'
                }
            }
        },
        series: [
            // 底层发光效果
            {
                data: temperatures,
                type: 'line',
                smooth: false,
                symbol: 'none',
                symbolSize: 0,
                lineStyle: {
                    color: colors.primary,
                    width: 2,
                    opacity: 0.2,
                    shadowColor: colors.shadow,
                    shadowBlur: 5,
                    shadowOffsetX: 0,
                    shadowOffsetY: 0
                },
                z: 1
            },
            // 主折线
            {
                data: temperatures,
                type: 'line',
                smooth: false,
                symbol: 'none',
                symbolSize: 0,
                lineStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 1,
                        y2: 0,
                        colorStops: [
                            { offset: 0, color: colors.primary },
                            { offset: 1, color: colors.secondary }
                        ]
                    },
                    width: 3,
                    shadowColor: colors.shadow,
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowOffsetY: 0
                },
                itemStyle: {
                    color: colors.primary,
                    borderColor: colors.primary,
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: colors.primary },
                            { offset: 0.3, color: colors.secondary },
                            { offset: 1, color: colors.light }
                        ]
                    }
                },
                z: 2,
                emphasis: {
                    focus: 'series',
                    itemStyle: {
                        color: colors.primary,
                        borderColor: '#ffffff',
                        borderWidth: 3,
                        shadowBlur: 5,
                        shadowColor: colors.shadow
                    }
                }
            }
        ],
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 25, 55, 0.9)',
            borderColor: 'rgba(39, 171, 255, 0.5)',
            borderWidth: 1,
            textStyle: {
                color: '#ffffff',
                fontSize: 12
            },
            formatter: function (params) {
                const data = params[0]
                return `时间: ${data.axisValue}<br/>数值: ${data.value}${props.unit}`
            }
        }
    }

    chartInstance.setOption(option)
}

onMounted(() => {
    initChart()

    // 响应式处理
    window.addEventListener('resize', () => {
        if (chartInstance) {
            chartInstance.resize()
        }
    })
})

// 监听props变化，重新初始化图表
watch([() => props.data, () => props.type, () => props.unit], () => {
    if (chartInstance) {
        initChart()
    }
}, { deep: true })

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
    }

    window.removeEventListener('resize', () => {
        if (chartInstance) {
            chartInstance.resize()
        }
    })
})
</script>

<style scoped lang="scss">
.temperature-chart {
    width: 100%;
    height: 179px;
}
</style>