body,
html {
  width: 100%;
  height: 100%;
  display: flex;
  margin: 0;
}
#app {
  width: 1920px;
  height: 1080px;
  color: #fff;
  position: fixed;
  top: 0;
  left: 50%;
  right: 0;
  bottom: 0;
  font-family: pingfang;
}
* {
  margin: 0;
  padding: 0;
}

/* 自定义滚动条样式 - 完全不影响布局 */
/* WebKit 浏览器 (Chrome, Safari, Edge) - 隐藏原生滚动条 */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 浏览器 - 隐藏滚动条 */
* {
  scrollbar-width: none;
  scrollbar-color: transparent transparent;
}

/* IE/Edge - 隐藏滚动条 */
* {
  -ms-overflow-style: none;
}

/* 为需要显示滚动条的元素单独设置 */
.show-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.show-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.show-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.6), rgba(147, 119, 255, 0.6));
  border-radius: 3px;
  transition: all 0.3s ease;
}

.show-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.8), rgba(147, 119, 255, 0.8));
  box-shadow: 0 0 6px rgba(64, 158, 255, 0.3);
}

.show-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(64, 158, 255, 0.6) rgba(255, 255, 255, 0.1);
}

/* Element Plus 组件滚动条适配 */
.el-scrollbar__wrap {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.el-scrollbar__wrap::-webkit-scrollbar {
  width: 0px !important;
  height: 0px !important;
}

/* 表格滚动条隐藏 */
.el-table__body-wrapper {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.el-table__body-wrapper::-webkit-scrollbar {
  width: 0px !important;
  height: 0px !important;
}

/* 下拉框滚动条隐藏 */
.el-select-dropdown__wrap {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.el-select-dropdown__wrap::-webkit-scrollbar {
  width: 0px !important;
  height: 0px !important;
}

.label-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: all;
  font-weight: 600;
  font-size: 20px;
  color: #FFFFFF;
  text-align: center;
}

.label-img {
  width: 46px;
  height: 46px;
  margin-top: 4px;
  object-fit: contain;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5));
}

.label-text {
  margin-bottom: 2px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  font-size: 14px;
  line-height: 1.2;
}