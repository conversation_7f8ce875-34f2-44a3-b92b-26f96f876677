<template>
  <!-- <img class="wangluo-img" src="@assets/images/wangluo.png" alt="" srcset=""> -->
  <div class="network-box"></div>
  <div class="wangluo-img"></div>
  <div class="sidebar-content">
    <!-- 搜索框 -->
    <div class="search-box">
      <div class="search-input-wrapper">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <circle cx="7" cy="7" r="6" stroke="#7BCCFF" stroke-width="1.5" />
          <path d="m13 13 4.35 4.35" stroke="#7BCCFF" stroke-width="1.5" />
        </svg>
        <input type="text" class="search-input" placeholder="搜索机柜名称" v-model="searchKeyword" />
      </div>
    </div>

    <!-- 机房列表 -->
    <div class="room-list">
      <div class="room-item" v-for="room in roomList" :key="room.id">
        <div class="room-header" @click="toggleRoom(room.id)">
          <div class="room-icon">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
              <rect x="2" y="3" width="10" height="8" rx="1" stroke="#27ABFF" stroke-width="1.5" fill="none" />
              <rect x="4" y="1" width="6" height="2" rx="0.5" stroke="#27ABFF" stroke-width="1.5" fill="none" />
            </svg>
          </div>
          <span class="room-name">{{ room.name }}</span>
          <div class="room-arrow" :class="{ 'expanded': room.expanded }">
            <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
              <path d="M2 3L4 5L6 3" stroke="#7BCCFF" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </div>
        </div>

        <div class="device-list" v-show="room.expanded">
          <div class="device-item" v-for="device in getFilteredDevices(room.devices)" :key="device.id"
            @click="selectDevice(device)" :class="{ 'selected': selectedDevice?.id === device.id }">
            <div class="device-icon">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <rect x="1" y="2" width="10" height="8" rx="1" stroke="#7BCCFF" stroke-width="1.5" fill="none" />
                <circle cx="3" cy="4" r="0.5" fill="#27ABFF" />
                <circle cx="3" cy="6" r="0.5" fill="#27ABFF" />
                <circle cx="3" cy="8" r="0.5" fill="#27ABFF" />
              </svg>
            </div>
            <span class="device-name">{{ device.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="right-content">
    <div class="container">
      <div class="container-item">
        <div class="content-header">
          网络监测情况
        </div>
        <div class="content-body wangluo-box">
          <div class="wangluo-item">
            <span class="wangluo-item-title">网络设备总数：</span>
            <span class="wangluo-item-value">15</span>
            <span class="wangluo-item-unit">台</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">当前在线数：</span>
            <span class="wangluo-item-value">13</span>
            <span class="wangluo-item-unit">台</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">网络警告数：</span>
            <span class="wangluo-item-value">15</span>
            <span class="wangluo-item-unit">台</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">主链路带宽：</span>
            <span class="wangluo-item-value">620 Mbps / 1000 Mbps(62%)</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">平均延迟：</span>
            <span class="wangluo-item-value">8</span>
            <span class="wangluo-item-unit">ms</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">丢包率：</span>
            <span class="wangluo-item-value">0.8</span>
            <span class="wangluo-item-unit">%</span>
          </div>
          <div class="wangluo-item">
            <span class="wangluo-item-title">最近异常：</span>
            <span class="wangluo-item-value">交换机SW-A02离线(10:52)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'

const router = useRouter()

const searchKeyword = ref('')
const selectedDevice = ref(null)

// 机房列表数据
const roomList = ref([
  {
    id: 'roomA',
    name: '机房A',
    expanded: true,
    devices: [
      { id: 'IT柜_001', name: '机柜1' },
      { id: 'IT柜_015', name: '机柜15' },
    ]
  },
])


// 切换机房展开状态
const toggleRoom = (roomId) => {
  const room = roomList.value.find(r => r.id === roomId)
  if (room) {
    room.expanded = !room.expanded
  }
}

// 选择设备
const selectDevice = (device) => {
  selectedDevice.value = device
  console.log('选择设备:', device)

  // 发送设备选择事件给ThreeScene
  const deviceSelectEvent = new CustomEvent('deviceSelected', {
    detail: {
      deviceId: device.id,
      deviceName: device.name
    }
  })
  window.dispatchEvent(deviceSelectEvent)
}

// 过滤设备列表
const getFilteredDevices = (devices) => {
  if (!searchKeyword.value) {
    return devices
  }
  return devices.filter(device =>
    device.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
}

</script>

<style scoped>

.wangluo-img {
  position: absolute;
  left: 0px;
  width: 100%;
  height: 100%;
  background: url('@assets/images/wangluo-img1.png') no-repeat center center / 100% 100%;
}

.right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 430px;
  height: 100%;
  background: url('@assets/images/right-bg.png') no-repeat center center / 100% 100%;
  pointer-events: all;
  display: flex;

  .container {
    margin-right: 17px;
    margin-left: auto;
  }
}

.container {
  width: 430px;
  height: 968px;
  background: linear-gradient(271deg, rgba(6, 25, 55, 0.3) 0%, rgba(9, 61, 102, 0.4) 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.container-item {}

.content-header {
  width: 100%;
  height: 54px;
  background: url('@assets/images/title-bg.png') no-repeat center center / 100% 100%;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  line-height: 54px;
  padding-left: 34px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .arrow-right-box {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .arrow-right {
    width: 17px;
    height: 17px;
    cursor: pointer;
  }
}


.network-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
}

.device-sidebar {
  position: absolute;
  top: 0;
  left: -37px;
  width: 40px;
  height: 80%;
  margin-top: 10%;
  transition: width 0.3s ease;
  z-index: 10;

  &.expanded {
    width: 238px;
    left: -280px;
  }
}

.sidebar-content {
  pointer-events: all;
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100%;
  backdrop-filter: blur(15px);
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;

  background: url('@assets/images/left-bg2.png') no-repeat center center / 100% 100%;

  /* background: linear-gradient( 271deg, rgba(6,25,55,0.46) 0%, #093D66 100%); */
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1;

  /* background: linear-gradient(271deg, rgba(6, 25, 55, 0.46) 0%, #093D66 100%);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(27, 83, 129, 0.64), rgba(39, 171, 255, 0.59), rgba(65, 239, 255, 0.58), rgba(39, 171, 255, 0.08), rgba(39, 171, 255, 0.01)) 1 1; */

}

.search-box {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  height: 40px;
  background: rgba(20, 53, 78, 0.8);
  border: 1px solid rgba(123, 204, 255, 0.3);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;

  &:focus-within {
    border-color: rgba(39, 171, 255, 0.6);
    box-shadow: 0 0 8px rgba(39, 171, 255, 0.2);
  }
}

.search-icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 14px;

  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }
}

.room-list {
  .room-item {
    margin-bottom: 8px;

    .room-header {
      display: flex;
      align-items: center;
      padding: 12px 8px;
      background: rgba(20, 53, 78, 0.6);
      border: 1px solid rgba(39, 171, 255, 0.2);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(39, 171, 255, 0.1);
        border-color: rgba(39, 171, 255, 0.4);
      }
    }

    .room-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .room-name {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;
    }

    .room-arrow {
      transition: transform 0.3s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}

.device-list {
  margin-top: 4px;
  padding-left: 8px;

  .device-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 2px;
    background: rgba(14, 38, 67, 0.4);
    border: 1px solid rgba(123, 204, 255, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(39, 171, 255, 0.1);
      border-color: rgba(39, 171, 255, 0.3);
    }

    &.selected {
      background: rgba(39, 171, 255, 0.2);
      border-color: rgba(39, 171, 255, 0.5);
    }

    .device-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .device-name {
      flex: 1;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.wangluo-box {
  padding-top: 18px;

  .wangluo-item {
    width: 100%;
    height: 40px;
    background: url('@assets/images/wangluo-item-bg.png') no-repeat center center / 100% 100%;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .wangluo-item-title {
      font-weight: 400;
      font-size: 14px;
      width: 100px;
      margin-left: 61px;
    }

    .wangluo-item-value {
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 20px;
      color: #41EFFF;
      letter-spacing: 1px;
    }

    .wangluo-item-unit {
      font-weight: 400;
      font-size: 14px;
      margin-left: 5px;
    }
  }
}
</style>